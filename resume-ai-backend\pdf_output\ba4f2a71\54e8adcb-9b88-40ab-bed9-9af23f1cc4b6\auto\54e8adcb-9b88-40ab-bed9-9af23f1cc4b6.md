# 基本信息

姓 名：何祥   
性 别：男   
邮 箱：<EMAIL>

出生年月：1997.10电 话：15151434422工作年限：5 年

# 专业技能

1. 熟悉软件测试过程，熟练掌握测试相关理论及工具，能够独立负责多个项目，具有 2 年 团队管理经验  
2. 熟练测试需求分析、测试计划编写、测试方案及用例的编写、熟悉基于项目特性用例组织，测试执行及测试报告的撰写等；  
3. 熟悉黑盒测试设计方法进行测试设计，如:等价类、边界值法、错误推断法、场景法、因果图判定法等熟悉禅道、Redmine 等各种缺陷工具对 Bug 的记录与跟踪，回归验证，以及问题的反向分析，风险识别和建议；  
4. 熟悉 Linux环境，能够独立编写 Shell，熟练使用 Docker 搭建开发测试环境  
5. 熟悉 Oracle、Mysql 数据库 SQL 语法应用，熟练使用存储过程完成测试数据构建，掌握常用 SQL 优化方法；  
6. 熟悉 HTTP 协议，熟悉接口测试流程，接口用例设计，熟练使用 Postman、jmeter 执行接口测试 ；  
7. 熟悉自动化测试设计，基于整个项目组的自动化工程能力建设，可实现自动化用例设计的有效性，重复性，可维护性等，能够独立实现 web 自动化和接口自动化框架的搭建并落地和应用，提高效率。熟悉常用的 python自动化框架，熟练基于 Python 编程语言的自动化框架；  
8. 熟悉测试工具开发过程，熟练使用 Python 结合 Pandas、Numpy、Faker、Pyhive、cx_oracle、xlrd、xlwd等完成测试工具开发，提升测试效率；  
9. 熟悉 Django，Vue3，能够独立完成测试平台设计开发；掌握常用的维修检测设备及方法,如万用表、示波器,能熟练测量各类电气信号。  
10. 熟练使用 PLC、伺服、变频器、机械手等自动化控制系统的配置、编程、调试,解决故障时需要对程序改写或参数调整。  
11. 熟练阅读电路图、原理图等技术资料,能快速理解自动化硬件的连接原理。

# 工作经历

软通动力集团有限公司 软件工程师 2024.01 - 至今朗新科技集团股份有限公司 测试专家 2021.02 - 2023.12苏州华兴源创股份有限公司 技术支持工程师 2019.06 - 2020.11

# 项目经验

项目一: 鸿蒙 QT 适配

项目描述: Qt For OpenHarmony 项目旨在将 Qt 框架与 OpenHarmony 操作系统融合，提供跨平台应用开发 支持，服务内容包括补充开发工具、适配核心模块，以及优化应用管理流程，旨在吸引 Qt 开发者社区，丰富Ope nHarmony 应用生态，开发者可以利用 Qt 的强大功能和易用性来开发适用于 OpenHarmony 系统的应用程序， 从而推动 OpenHarmony 生态系统的发展。

# 主要职责：

1. 1. 负责 Qt 单元测试用例鸿蒙化改造，使用 Cmake 结合鸿蒙工程移植 Qt 单元测试代码到鸿蒙生态，使用 Python 结合 hdc 命令编写单元测试自动化执行脚本

2. 负责 Qt 示例鸿蒙化改造，使用 Cmake 结合鸿蒙工程移植 Qt 示例到鸿蒙生态；3. 负责鸿蒙 QT SDK 适配单元测试执行，编写 Python 脚本提取 xml 单元测试结果，功能测试执行，回归测 试执行，编写每个版本的测试报告；

项目二: 新一代用电采集系统

项目描述: 新一代用电信息采集系统（简称采集 2.0）以国网公司战略为统领，遵循“抽象分层、分类分级、智能支撑、融合应用”的原则，按照“数字驱动、价值 闭环”的设计理念，以核心业务需求为前提，将业务需求进行分解、抽象、归集，形成具有“沉淀、赋能、共享”特性的业务架构，通 过“面向对象”设计方法，设计成性能卓越、功能丰富、安全稳定的客户侧能源互联网基础系统，支持各类用能设备的全接入、全采集、全控制，支持“碳达峰、碳中和”行动方案落地、电力市场化改革、清洁能源消纳和用户能效诊断，服务公司各部门、各专业、各类员工的数据和业务需求。

# 主要职责:

1. 测试需求分析，测试用例设计与评审组织，编写测试报告，完成系统功能全量测试  
2. 测试环境基于业务逻辑与存储过程完成大批量数据准备，配置定时任务，验证数据链路有效性  
3. 测试环境版本更新维护  
4. 结合日志、F12 定位 Bug，提出 Bug 有效性解决方案  
5. 在内网环境使用 MobaXtarm完成多服务组件之间的链路测试，保证报文传输链路正常  
6. 完成电力 376.1、698 通讯协议专项测试  
7. 编制用户手册，用户培训，协助用户完成系统验收

项目三: A 客户 BMS 测试

项目描述: BMS是 A 客户为提升电池寿命研发的新一代产品。该项目承接的是封装完成后的最后一道测试工序，公司根据客户提出的指标 研发了专为 BMS 芯片而生的 Tester，结合自主研发的自动化生产线，保证了测试准确率的同时使生产效率达到指标之上，使得 改项目延续至今

# 主要职责：

1. 出厂测试，测试机出厂时程序的烧录，调整设备参数  
2. 设备立项，协调现场人员设备安装，与客户沟通，反馈客户需求  
3. 负责现场设备运行调优，PLC 程序优化、HMI 功能优化  
4. 负责现场电气接线指导，结构调试，机械手打点，视觉对位模块调试  
5. 现场通信调试，BMS 监控系统，上位机软件、数据库等多个软件安装  
6. 现场测试机异常项统计，及时矫正参数，保证测试机正常运行

# 教育背景

中国矿业大学 本科 2020.2 - 2022.6  
徐州工业职业技术学院 大专 2016.9 - 2019.6