{"pdf_info": [{"preproc_blocks": [{"type": "title", "bbox": [49, 46, 107, 63], "lines": [{"bbox": [48, 45, 108, 64], "spans": [{"bbox": [48, 45, 108, 64], "score": 1.0, "content": "基本信息", "type": "text"}], "index": 0}], "index": 0}, {"type": "text", "bbox": [34, 68, 208, 119], "lines": [{"bbox": [34, 67, 113, 84], "spans": [{"bbox": [34, 68, 50, 83], "score": 1.0, "content": "姓", "type": "text"}, {"bbox": [64, 67, 113, 84], "score": 1.0, "content": "名：何祥", "type": "text"}], "index": 1}, {"bbox": [34, 85, 102, 102], "spans": [{"bbox": [34, 86, 50, 101], "score": 1.0, "content": "性", "type": "text"}, {"bbox": [64, 85, 102, 102], "score": 1.0, "content": "别：男", "type": "text"}], "index": 3}, {"bbox": [34, 104, 208, 120], "spans": [{"bbox": [34, 104, 49, 119], "score": 1.0, "content": "邮", "type": "text"}, {"bbox": [65, 104, 208, 120], "score": 1.0, "content": "箱：<EMAIL>", "type": "text"}], "index": 5}], "index": 3}, {"type": "text", "bbox": [300, 68, 425, 119], "lines": [{"bbox": [302, 69, 396, 83], "spans": [{"bbox": [302, 69, 396, 83], "score": 1.0, "content": "出生年月：1997.10", "type": "text"}], "index": 2}, {"bbox": [300, 87, 425, 101], "spans": [{"bbox": [300, 87, 425, 101], "score": 1.0, "content": "电 话：15151434422", "type": "text"}], "index": 4}, {"bbox": [302, 105, 376, 119], "spans": [{"bbox": [302, 105, 376, 119], "score": 1.0, "content": "工作年限：5 年", "type": "text"}], "index": 6}], "index": 4}, {"type": "title", "bbox": [49, 153, 107, 171], "lines": [{"bbox": [48, 153, 109, 172], "spans": [{"bbox": [48, 153, 109, 172], "score": 1.0, "content": "专业技能", "type": "text"}], "index": 7}], "index": 7}, {"type": "text", "bbox": [34, 175, 562, 558], "lines": [{"bbox": [35, 176, 520, 190], "spans": [{"bbox": [35, 176, 520, 190], "score": 1.0, "content": "1. 熟悉软件测试过程，熟练掌握测试相关理论及工具，能够独立负责多个项目，具有 2 年 团队管理经验", "type": "text"}], "index": 8}, {"bbox": [35, 195, 559, 208], "spans": [{"bbox": [35, 195, 559, 208], "score": 1.0, "content": "2. 熟练测试需求分析、测试计划编写、测试方案及用例的编写、熟悉基于项目特性用例组织，测试执行及测试报", "type": "text"}], "index": 9}, {"bbox": [49, 212, 109, 227], "spans": [{"bbox": [49, 212, 109, 227], "score": 1.0, "content": "告的撰写等；", "type": "text"}], "index": 10}, {"bbox": [35, 234, 557, 247], "spans": [{"bbox": [35, 234, 557, 247], "score": 1.0, "content": "3. 熟悉黑盒测试设计方法进行测试设计，如:等价类、边界值法、错误推断法、场景法、因果图判定法等熟悉禅道、", "type": "text"}], "index": 11}, {"bbox": [50, 256, 516, 269], "spans": [{"bbox": [50, 256, 516, 269], "score": 1.0, "content": "Redmine 等各种缺陷工具对 Bug 的记录与跟踪，回归验证，以及问题的反向分析，风险识别和建议；", "type": "text"}], "index": 12}, {"bbox": [34, 278, 403, 290], "spans": [{"bbox": [34, 278, 403, 290], "score": 1.0, "content": "4. 熟悉 Linux环境，能够独立编写 Shell，熟练使用 Docker 搭建开发测试环境", "type": "text"}], "index": 13}, {"bbox": [35, 299, 549, 312], "spans": [{"bbox": [35, 299, 549, 312], "score": 1.0, "content": "5. 熟悉 Oracle、Mysql 数据库 SQL 语法应用，熟练使用存储过程完成测试数据构建，掌握常用 SQL 优化方法；", "type": "text"}], "index": 14}, {"bbox": [34, 320, 512, 334], "spans": [{"bbox": [34, 320, 512, 334], "score": 1.0, "content": "6. 熟悉 HTTP 协议，熟悉接口测试流程，接口用例设计，熟练使用 Postman、jmeter 执行接口测试 ；", "type": "text"}], "index": 15}, {"bbox": [36, 340, 558, 351], "spans": [{"bbox": [36, 340, 558, 351], "score": 1.0, "content": "7. 熟悉自动化测试设计，基于整个项目组的自动化工程能力建设，可实现自动化用例设计的有效性，重复性，可", "type": "text"}], "index": 16}, {"bbox": [48, 359, 560, 374], "spans": [{"bbox": [48, 359, 560, 374], "score": 1.0, "content": "维护性等，能够独立实现 web 自动化和接口自动化框架的搭建并落地和应用，提高效率。熟悉常用的 python", "type": "text"}], "index": 17}, {"bbox": [50, 379, 308, 394], "spans": [{"bbox": [50, 379, 308, 394], "score": 1.0, "content": "自动化框架，熟练基于 Python 编程语言的自动化框架；", "type": "text"}], "index": 18}, {"bbox": [33, 403, 560, 419], "spans": [{"bbox": [33, 403, 560, 419], "score": 1.0, "content": "8. 熟悉测试工具开发过程，熟练使用 Python 结合 Pandas、Numpy、Faker、Pyhive、cx_oracle、xlrd、xlwd", "type": "text"}], "index": 19}, {"bbox": [50, 426, 224, 439], "spans": [{"bbox": [50, 426, 224, 439], "score": 1.0, "content": "等完成测试工具开发，提升测试效率；", "type": "text"}], "index": 20}, {"bbox": [34, 447, 559, 462], "spans": [{"bbox": [34, 447, 559, 462], "score": 1.0, "content": "9. 熟悉 Django，Vue3，能够独立完成测试平台设计开发；掌握常用的维修检测设备及方法,如万用表、示波器,", "type": "text"}], "index": 21}, {"bbox": [48, 468, 173, 483], "spans": [{"bbox": [48, 468, 173, 483], "score": 1.0, "content": "能熟练测量各类电气信号。", "type": "text"}], "index": 22}, {"bbox": [36, 491, 559, 504], "spans": [{"bbox": [36, 491, 559, 504], "score": 1.0, "content": "10. 熟练使用 PLC、伺服、变频器、机械手等自动化控制系统的配置、编程、调试,解决故障时需要对程序改写或参", "type": "text"}], "index": 23}, {"bbox": [48, 512, 89, 527], "spans": [{"bbox": [48, 512, 89, 527], "score": 1.0, "content": "数调整。", "type": "text"}], "index": 24}, {"bbox": [35, 534, 389, 547], "spans": [{"bbox": [35, 534, 389, 547], "score": 1.0, "content": "11. 熟练阅读电路图、原理图等技术资料,能快速理解自动化硬件的连接原理。", "type": "text"}], "index": 25}], "index": 16.5}, {"type": "title", "bbox": [48, 564, 108, 582], "lines": [{"bbox": [47, 563, 108, 583], "spans": [{"bbox": [47, 563, 108, 583], "score": 1.0, "content": "工作经历", "type": "text"}], "index": 26}], "index": 26}, {"type": "text", "bbox": [34, 591, 489, 644], "lines": [{"bbox": [35, 592, 469, 607], "spans": [{"bbox": [35, 593, 143, 606], "score": 1.0, "content": "软通动力集团有限公司", "type": "text"}, {"bbox": [212, 592, 269, 607], "score": 1.0, "content": "软件工程师", "type": "text"}, {"bbox": [389, 592, 469, 607], "score": 1.0, "content": "2024.01 - 至今", "type": "text"}], "index": 27}, {"bbox": [36, 610, 488, 626], "spans": [{"bbox": [36, 611, 164, 624], "score": 1.0, "content": "朗新科技集团股份有限公司", "type": "text"}, {"bbox": [212, 610, 258, 626], "score": 1.0, "content": "测试专家", "type": "text"}, {"bbox": [390, 610, 488, 624], "score": 1.0, "content": "2021.02 - 2023.12", "type": "text"}], "index": 28}, {"bbox": [34, 628, 486, 643], "spans": [{"bbox": [34, 629, 164, 643], "score": 1.0, "content": "苏州华兴源创股份有限公司", "type": "text"}, {"bbox": [212, 628, 289, 643], "score": 1.0, "content": "技术支持工程师", "type": "text"}, {"bbox": [390, 629, 486, 642], "score": 1.0, "content": "2019.06 - 2020.11", "type": "text"}], "index": 29}], "index": 28}, {"type": "title", "bbox": [48, 660, 108, 677], "lines": [{"bbox": [47, 659, 109, 678], "spans": [{"bbox": [47, 659, 109, 678], "score": 1.0, "content": "项目经验", "type": "text"}], "index": 30}], "index": 30}, {"type": "text", "bbox": [35, 687, 150, 702], "lines": [{"bbox": [34, 687, 150, 703], "spans": [{"bbox": [34, 687, 150, 703], "score": 1.0, "content": "项目一: 鸿蒙 QT 适配", "type": "text"}], "index": 31}], "index": 31}, {"type": "text", "bbox": [34, 705, 561, 775], "lines": [{"bbox": [35, 706, 560, 721], "spans": [{"bbox": [35, 706, 560, 721], "score": 1.0, "content": "项目描述: Qt For OpenHarmony 项目旨在将 Qt 框架与 OpenHarmony 操作系统融合，提供跨平台应用开", "type": "text"}], "index": 32}, {"bbox": [35, 725, 559, 738], "spans": [{"bbox": [35, 725, 559, 738], "score": 1.0, "content": "发 支持，服务内容包括补充开发工具、适配核心模块，以及优化应用管理流程，旨在吸引 Qt 开发者社区，丰富", "type": "text"}], "index": 33}, {"bbox": [41, 742, 560, 757], "spans": [{"bbox": [41, 742, 560, 757], "score": 1.0, "content": "Ope nHarmony 应用生态，开发者可以利用 Qt 的强大功能和易用性来开发适用于 OpenHarmony 系统的应", "type": "text"}], "index": 34}, {"bbox": [35, 760, 291, 775], "spans": [{"bbox": [35, 760, 291, 775], "score": 1.0, "content": "用程序， 从而推动 OpenHarmony 生态系统的发展。", "type": "text"}], "index": 35}], "index": 33.5}, {"type": "title", "bbox": [35, 778, 83, 792], "lines": [{"bbox": [34, 778, 86, 793], "spans": [{"bbox": [34, 778, 86, 793], "score": 1.0, "content": "主要职责：", "type": "text"}], "index": 36}], "index": 36}, {"type": "text", "bbox": [56, 797, 555, 814], "lines": [{"bbox": [57, 798, 553, 812], "spans": [{"bbox": [57, 798, 553, 812], "score": 1.0, "content": "1. 1. 负责 Qt 单元测试用例鸿蒙化改造，使用 Cmake 结合鸿蒙工程移植 Qt 单元测试代码到鸿蒙生态，", "type": "text"}], "index": 37}], "index": 37}], "page_idx": 0, "page_size": [595, 841], "discarded_blocks": [], "para_blocks": [{"type": "title", "bbox": [49, 46, 107, 63], "lines": [{"bbox": [48, 45, 108, 64], "spans": [{"bbox": [48, 45, 108, 64], "score": 1.0, "content": "基本信息", "type": "text"}], "index": 0}], "index": 0}, {"type": "list", "bbox": [34, 68, 208, 119], "lines": [{"bbox": [34, 67, 113, 84], "spans": [{"bbox": [34, 68, 50, 83], "score": 1.0, "content": "姓", "type": "text"}, {"bbox": [64, 67, 113, 84], "score": 1.0, "content": "名：何祥", "type": "text"}], "index": 1, "is_list_start_line": true}, {"bbox": [34, 85, 102, 102], "spans": [{"bbox": [34, 86, 50, 101], "score": 1.0, "content": "性", "type": "text"}, {"bbox": [64, 85, 102, 102], "score": 1.0, "content": "别：男", "type": "text"}], "index": 3, "is_list_start_line": true}, {"bbox": [34, 104, 208, 120], "spans": [{"bbox": [34, 104, 49, 119], "score": 1.0, "content": "邮", "type": "text"}, {"bbox": [65, 104, 208, 120], "score": 1.0, "content": "箱：<EMAIL>", "type": "text"}], "index": 5, "is_list_start_line": true}], "index": 3, "bbox_fs": [34, 67, 208, 120]}, {"type": "text", "bbox": [300, 68, 425, 119], "lines": [{"bbox": [302, 69, 396, 83], "spans": [{"bbox": [302, 69, 396, 83], "score": 1.0, "content": "出生年月：1997.10", "type": "text"}], "index": 2}, {"bbox": [300, 87, 425, 101], "spans": [{"bbox": [300, 87, 425, 101], "score": 1.0, "content": "电 话：15151434422", "type": "text"}], "index": 4}, {"bbox": [302, 105, 376, 119], "spans": [{"bbox": [302, 105, 376, 119], "score": 1.0, "content": "工作年限：5 年", "type": "text"}], "index": 6}], "index": 4, "bbox_fs": [300, 69, 425, 119]}, {"type": "title", "bbox": [49, 153, 107, 171], "lines": [{"bbox": [48, 153, 109, 172], "spans": [{"bbox": [48, 153, 109, 172], "score": 1.0, "content": "专业技能", "type": "text"}], "index": 7}], "index": 7}, {"type": "list", "bbox": [34, 175, 562, 558], "lines": [{"bbox": [35, 176, 520, 190], "spans": [{"bbox": [35, 176, 520, 190], "score": 1.0, "content": "1. 熟悉软件测试过程，熟练掌握测试相关理论及工具，能够独立负责多个项目，具有 2 年 团队管理经验", "type": "text"}], "index": 8, "is_list_start_line": true, "is_list_end_line": true}, {"bbox": [35, 195, 559, 208], "spans": [{"bbox": [35, 195, 559, 208], "score": 1.0, "content": "2. 熟练测试需求分析、测试计划编写、测试方案及用例的编写、熟悉基于项目特性用例组织，测试执行及测试报", "type": "text"}], "index": 9, "is_list_start_line": true}, {"bbox": [49, 212, 109, 227], "spans": [{"bbox": [49, 212, 109, 227], "score": 1.0, "content": "告的撰写等；", "type": "text"}], "index": 10, "is_list_end_line": true}, {"bbox": [35, 234, 557, 247], "spans": [{"bbox": [35, 234, 557, 247], "score": 1.0, "content": "3. 熟悉黑盒测试设计方法进行测试设计，如:等价类、边界值法、错误推断法、场景法、因果图判定法等熟悉禅道、", "type": "text"}], "index": 11, "is_list_start_line": true}, {"bbox": [50, 256, 516, 269], "spans": [{"bbox": [50, 256, 516, 269], "score": 1.0, "content": "Redmine 等各种缺陷工具对 Bug 的记录与跟踪，回归验证，以及问题的反向分析，风险识别和建议；", "type": "text"}], "index": 12, "is_list_end_line": true}, {"bbox": [34, 278, 403, 290], "spans": [{"bbox": [34, 278, 403, 290], "score": 1.0, "content": "4. 熟悉 Linux环境，能够独立编写 Shell，熟练使用 Docker 搭建开发测试环境", "type": "text"}], "index": 13, "is_list_start_line": true, "is_list_end_line": true}, {"bbox": [35, 299, 549, 312], "spans": [{"bbox": [35, 299, 549, 312], "score": 1.0, "content": "5. 熟悉 Oracle、Mysql 数据库 SQL 语法应用，熟练使用存储过程完成测试数据构建，掌握常用 SQL 优化方法；", "type": "text"}], "index": 14, "is_list_start_line": true}, {"bbox": [34, 320, 512, 334], "spans": [{"bbox": [34, 320, 512, 334], "score": 1.0, "content": "6. 熟悉 HTTP 协议，熟悉接口测试流程，接口用例设计，熟练使用 Postman、jmeter 执行接口测试 ；", "type": "text"}], "index": 15, "is_list_start_line": true, "is_list_end_line": true}, {"bbox": [36, 340, 558, 351], "spans": [{"bbox": [36, 340, 558, 351], "score": 1.0, "content": "7. 熟悉自动化测试设计，基于整个项目组的自动化工程能力建设，可实现自动化用例设计的有效性，重复性，可", "type": "text"}], "index": 16, "is_list_start_line": true}, {"bbox": [48, 359, 560, 374], "spans": [{"bbox": [48, 359, 560, 374], "score": 1.0, "content": "维护性等，能够独立实现 web 自动化和接口自动化框架的搭建并落地和应用，提高效率。熟悉常用的 python", "type": "text"}], "index": 17}, {"bbox": [50, 379, 308, 394], "spans": [{"bbox": [50, 379, 308, 394], "score": 1.0, "content": "自动化框架，熟练基于 Python 编程语言的自动化框架；", "type": "text"}], "index": 18, "is_list_end_line": true}, {"bbox": [33, 403, 560, 419], "spans": [{"bbox": [33, 403, 560, 419], "score": 1.0, "content": "8. 熟悉测试工具开发过程，熟练使用 Python 结合 Pandas、Numpy、Faker、Pyhive、cx_oracle、xlrd、xlwd", "type": "text"}], "index": 19, "is_list_start_line": true}, {"bbox": [50, 426, 224, 439], "spans": [{"bbox": [50, 426, 224, 439], "score": 1.0, "content": "等完成测试工具开发，提升测试效率；", "type": "text"}], "index": 20, "is_list_end_line": true}, {"bbox": [34, 447, 559, 462], "spans": [{"bbox": [34, 447, 559, 462], "score": 1.0, "content": "9. 熟悉 Django，Vue3，能够独立完成测试平台设计开发；掌握常用的维修检测设备及方法,如万用表、示波器,", "type": "text"}], "index": 21, "is_list_start_line": true}, {"bbox": [48, 468, 173, 483], "spans": [{"bbox": [48, 468, 173, 483], "score": 1.0, "content": "能熟练测量各类电气信号。", "type": "text"}], "index": 22, "is_list_end_line": true}, {"bbox": [36, 491, 559, 504], "spans": [{"bbox": [36, 491, 559, 504], "score": 1.0, "content": "10. 熟练使用 PLC、伺服、变频器、机械手等自动化控制系统的配置、编程、调试,解决故障时需要对程序改写或参", "type": "text"}], "index": 23, "is_list_start_line": true}, {"bbox": [48, 512, 89, 527], "spans": [{"bbox": [48, 512, 89, 527], "score": 1.0, "content": "数调整。", "type": "text"}], "index": 24, "is_list_end_line": true}, {"bbox": [35, 534, 389, 547], "spans": [{"bbox": [35, 534, 389, 547], "score": 1.0, "content": "11. 熟练阅读电路图、原理图等技术资料,能快速理解自动化硬件的连接原理。", "type": "text"}], "index": 25, "is_list_start_line": true, "is_list_end_line": true}], "index": 16.5, "bbox_fs": [33, 176, 560, 547]}, {"type": "title", "bbox": [48, 564, 108, 582], "lines": [{"bbox": [47, 563, 108, 583], "spans": [{"bbox": [47, 563, 108, 583], "score": 1.0, "content": "工作经历", "type": "text"}], "index": 26}], "index": 26}, {"type": "text", "bbox": [34, 591, 489, 644], "lines": [{"bbox": [35, 592, 469, 607], "spans": [{"bbox": [35, 593, 143, 606], "score": 1.0, "content": "软通动力集团有限公司", "type": "text"}, {"bbox": [212, 592, 269, 607], "score": 1.0, "content": "软件工程师", "type": "text"}, {"bbox": [389, 592, 469, 607], "score": 1.0, "content": "2024.01 - 至今", "type": "text"}], "index": 27}, {"bbox": [36, 610, 488, 626], "spans": [{"bbox": [36, 611, 164, 624], "score": 1.0, "content": "朗新科技集团股份有限公司", "type": "text"}, {"bbox": [212, 610, 258, 626], "score": 1.0, "content": "测试专家", "type": "text"}, {"bbox": [390, 610, 488, 624], "score": 1.0, "content": "2021.02 - 2023.12", "type": "text"}], "index": 28}, {"bbox": [34, 628, 486, 643], "spans": [{"bbox": [34, 629, 164, 643], "score": 1.0, "content": "苏州华兴源创股份有限公司", "type": "text"}, {"bbox": [212, 628, 289, 643], "score": 1.0, "content": "技术支持工程师", "type": "text"}, {"bbox": [390, 629, 486, 642], "score": 1.0, "content": "2019.06 - 2020.11", "type": "text"}], "index": 29}], "index": 28, "bbox_fs": [34, 592, 488, 643]}, {"type": "title", "bbox": [48, 660, 108, 677], "lines": [{"bbox": [47, 659, 109, 678], "spans": [{"bbox": [47, 659, 109, 678], "score": 1.0, "content": "项目经验", "type": "text"}], "index": 30}], "index": 30}, {"type": "text", "bbox": [35, 687, 150, 702], "lines": [{"bbox": [34, 687, 150, 703], "spans": [{"bbox": [34, 687, 150, 703], "score": 1.0, "content": "项目一: 鸿蒙 QT 适配", "type": "text"}], "index": 31}], "index": 31, "bbox_fs": [34, 687, 150, 703]}, {"type": "text", "bbox": [34, 705, 561, 775], "lines": [{"bbox": [35, 706, 560, 721], "spans": [{"bbox": [35, 706, 560, 721], "score": 1.0, "content": "项目描述: Qt For OpenHarmony 项目旨在将 Qt 框架与 OpenHarmony 操作系统融合，提供跨平台应用开", "type": "text"}], "index": 32}, {"bbox": [35, 725, 559, 738], "spans": [{"bbox": [35, 725, 559, 738], "score": 1.0, "content": "发 支持，服务内容包括补充开发工具、适配核心模块，以及优化应用管理流程，旨在吸引 Qt 开发者社区，丰富", "type": "text"}], "index": 33}, {"bbox": [41, 742, 560, 757], "spans": [{"bbox": [41, 742, 560, 757], "score": 1.0, "content": "Ope nHarmony 应用生态，开发者可以利用 Qt 的强大功能和易用性来开发适用于 OpenHarmony 系统的应", "type": "text"}], "index": 34}, {"bbox": [35, 760, 291, 775], "spans": [{"bbox": [35, 760, 291, 775], "score": 1.0, "content": "用程序， 从而推动 OpenHarmony 生态系统的发展。", "type": "text"}], "index": 35}], "index": 33.5, "bbox_fs": [35, 706, 560, 775]}, {"type": "title", "bbox": [35, 778, 83, 792], "lines": [{"bbox": [34, 778, 86, 793], "spans": [{"bbox": [34, 778, 86, 793], "score": 1.0, "content": "主要职责：", "type": "text"}], "index": 36}], "index": 36}, {"type": "text", "bbox": [56, 797, 555, 814], "lines": [{"bbox": [57, 798, 553, 812], "spans": [{"bbox": [57, 798, 553, 812], "score": 1.0, "content": "1. 1. 负责 Qt 单元测试用例鸿蒙化改造，使用 Cmake 结合鸿蒙工程移植 Qt 单元测试代码到鸿蒙生态，", "type": "text"}], "index": 37}, {"bbox": [70, 44, 348, 59], "spans": [{"bbox": [70, 44, 348, 59], "score": 1.0, "content": "使用 Python 结合 hdc 命令编写单元测试自动化执行脚本", "type": "text", "cross_page": true}], "index": 0}], "index": 37, "bbox_fs": [57, 798, 553, 812]}]}, {"preproc_blocks": [{"type": "text", "bbox": [69, 43, 348, 58], "lines": [{"bbox": [70, 44, 348, 59], "spans": [{"bbox": [70, 44, 348, 59], "score": 1.0, "content": "使用 Python 结合 hdc 命令编写单元测试自动化执行脚本", "type": "text"}], "index": 0}], "index": 0}, {"type": "text", "bbox": [54, 61, 560, 113], "lines": [{"bbox": [56, 63, 451, 75], "spans": [{"bbox": [56, 63, 451, 75], "score": 1.0, "content": "2. 负责 Qt 示例鸿蒙化改造，使用 Cmake 结合鸿蒙工程移植 Qt 示例到鸿蒙生态；", "type": "text"}], "index": 1}, {"bbox": [56, 81, 559, 93], "spans": [{"bbox": [56, 81, 559, 93], "score": 1.0, "content": "3. 负责鸿蒙 QT SDK 适配单元测试执行，编写 Python 脚本提取 xml 单元测试结果，功能测试执行，回", "type": "text"}], "index": 2}, {"bbox": [71, 99, 261, 111], "spans": [{"bbox": [71, 99, 261, 111], "score": 1.0, "content": "归测 试执行，编写每个版本的测试报告；", "type": "text"}], "index": 3}], "index": 2}, {"type": "text", "bbox": [35, 122, 179, 136], "lines": [{"bbox": [35, 123, 177, 136], "spans": [{"bbox": [35, 123, 177, 136], "score": 1.0, "content": "项目二: 新一代用电采集系统", "type": "text"}], "index": 4}], "index": 4}, {"type": "text", "bbox": [34, 139, 560, 245], "lines": [{"bbox": [36, 141, 558, 154], "spans": [{"bbox": [36, 141, 558, 154], "score": 1.0, "content": "项目描述: 新一代用电信息采集系统（简称采集 2.0）以国网公司战略为统领，遵循“抽象分层、分类分级、智", "type": "text"}], "index": 5}, {"bbox": [36, 159, 558, 170], "spans": [{"bbox": [36, 159, 558, 170], "score": 1.0, "content": "能支撑、融合应用”的原则，按照“数字驱动、价值 闭环”的设计理念，以核心业务需求为前提，将业务需求进", "type": "text"}], "index": 6}, {"bbox": [36, 177, 558, 190], "spans": [{"bbox": [36, 177, 558, 190], "score": 1.0, "content": "行分解、抽象、归集，形成具有“沉淀、赋能、共享”特性的业务架构，通 过“面向对象”设计方法，设计成性", "type": "text"}], "index": 7}, {"bbox": [36, 195, 559, 208], "spans": [{"bbox": [36, 195, 559, 208], "score": 1.0, "content": "能卓越、功能丰富、安全稳定的客户侧能源互联网基础系统，支持各类用能设备的全接入、全采集、全控制，支持", "type": "text"}], "index": 8}, {"bbox": [43, 213, 556, 226], "spans": [{"bbox": [43, 213, 556, 226], "score": 1.0, "content": "“碳达峰、碳中和”行动方案落地、电力市场化改革、清洁能源消纳和用户能效诊断，服务公司各部门、各专业、", "type": "text"}], "index": 9}, {"bbox": [36, 231, 168, 244], "spans": [{"bbox": [36, 231, 168, 244], "score": 1.0, "content": "各类员工的数据和业务需求。", "type": "text"}], "index": 10}], "index": 7.5}, {"type": "title", "bbox": [35, 249, 82, 262], "lines": [{"bbox": [34, 248, 84, 264], "spans": [{"bbox": [34, 248, 84, 264], "score": 1.0, "content": "主要职责:", "type": "text"}], "index": 11}], "index": 11}, {"type": "text", "bbox": [55, 266, 506, 389], "lines": [{"bbox": [56, 267, 449, 280], "spans": [{"bbox": [56, 267, 449, 280], "score": 1.0, "content": "1. 测试需求分析，测试用例设计与评审组织，编写测试报告，完成系统功能全量测试", "type": "text"}], "index": 12}, {"bbox": [55, 284, 501, 298], "spans": [{"bbox": [55, 284, 501, 298], "score": 1.0, "content": "2. 测试环境基于业务逻辑与存储过程完成大批量数据准备，配置定时任务，验证数据链路有效性", "type": "text"}], "index": 13}, {"bbox": [56, 303, 175, 316], "spans": [{"bbox": [56, 303, 175, 316], "score": 1.0, "content": "3. 测试环境版本更新维护", "type": "text"}], "index": 14}, {"bbox": [56, 321, 317, 335], "spans": [{"bbox": [56, 321, 317, 335], "score": 1.0, "content": "4. 结合日志、F12 定位 Bug，提出 Bug 有效性解决方案", "type": "text"}], "index": 15}, {"bbox": [56, 339, 471, 351], "spans": [{"bbox": [56, 339, 471, 351], "score": 1.0, "content": "5. 在内网环境使用 MobaXtarm完成多服务组件之间的链路测试，保证报文传输链路正常", "type": "text"}], "index": 16}, {"bbox": [56, 357, 257, 370], "spans": [{"bbox": [56, 357, 257, 370], "score": 1.0, "content": "6. 完成电力 376.1、698 通讯协议专项测试", "type": "text"}], "index": 17}, {"bbox": [56, 376, 301, 388], "spans": [{"bbox": [56, 376, 301, 388], "score": 1.0, "content": "7. 编制用户手册，用户培训，协助用户完成系统验收", "type": "text"}], "index": 18}], "index": 15}, {"type": "text", "bbox": [35, 398, 159, 412], "lines": [{"bbox": [35, 398, 158, 412], "spans": [{"bbox": [35, 398, 158, 412], "score": 1.0, "content": "项目三: A 客户 BMS 测试", "type": "text"}], "index": 19}], "index": 19}, {"type": "text", "bbox": [35, 416, 560, 467], "lines": [{"bbox": [35, 417, 555, 429], "spans": [{"bbox": [35, 417, 555, 429], "score": 1.0, "content": "项目描述: BMS是 A 客户为提升电池寿命研发的新一代产品。该项目承接的是封装完成后的最后一道测试工序，", "type": "text"}], "index": 20}, {"bbox": [36, 434, 559, 447], "spans": [{"bbox": [36, 434, 559, 447], "score": 1.0, "content": "公司根据客户提出的指标 研发了专为 BMS 芯片而生的 Tester，结合自主研发的自动化生产线，保证了测试准确", "type": "text"}], "index": 21}, {"bbox": [36, 453, 304, 465], "spans": [{"bbox": [36, 453, 304, 465], "score": 1.0, "content": "率的同时使生产效率达到指标之上，使得 改项目延续至今", "type": "text"}], "index": 22}], "index": 21}, {"type": "title", "bbox": [35, 470, 83, 484], "lines": [{"bbox": [34, 470, 86, 485], "spans": [{"bbox": [34, 470, 86, 485], "score": 1.0, "content": "主要职责：", "type": "text"}], "index": 23}], "index": 23}, {"type": "text", "bbox": [54, 490, 394, 596], "lines": [{"bbox": [56, 492, 314, 505], "spans": [{"bbox": [56, 492, 314, 505], "score": 1.0, "content": "1. 出厂测试，测试机出厂时程序的烧录，调整设备参数", "type": "text"}], "index": 24}, {"bbox": [55, 509, 368, 524], "spans": [{"bbox": [55, 509, 368, 524], "score": 1.0, "content": "2. 设备立项，协调现场人员设备安装，与客户沟通，反馈客户需求", "type": "text"}], "index": 25}, {"bbox": [56, 528, 329, 541], "spans": [{"bbox": [56, 528, 329, 541], "score": 1.0, "content": "3. 负责现场设备运行调优，PLC 程序优化、HMI 功能优化", "type": "text"}], "index": 26}, {"bbox": [55, 545, 389, 560], "spans": [{"bbox": [55, 545, 389, 560], "score": 1.0, "content": "4. 负责现场电气接线指导，结构调试，机械手打点，视觉对位模块调试", "type": "text"}], "index": 27}, {"bbox": [55, 563, 393, 577], "spans": [{"bbox": [55, 563, 393, 577], "score": 1.0, "content": "5. 现场通信调试，BMS 监控系统，上位机软件、数据库等多个软件安装", "type": "text"}], "index": 28}, {"bbox": [56, 582, 356, 595], "spans": [{"bbox": [56, 582, 356, 595], "score": 1.0, "content": "6. 现场测试机异常项统计，及时矫正参数，保证测试机正常运行", "type": "text"}], "index": 29}], "index": 26.5}, {"type": "title", "bbox": [48, 612, 107, 630], "lines": [{"bbox": [48, 612, 109, 630], "spans": [{"bbox": [48, 612, 109, 630], "score": 1.0, "content": "教育背景", "type": "text"}], "index": 30}], "index": 30}, {"type": "text", "bbox": [35, 639, 477, 673], "lines": [{"bbox": [35, 638, 475, 656], "spans": [{"bbox": [35, 640, 101, 654], "score": 1.0, "content": "中国矿业大学", "type": "text"}, {"bbox": [211, 638, 239, 656], "score": 1.0, "content": "本科", "type": "text"}, {"bbox": [390, 640, 475, 653], "score": 1.0, "content": "2020.2 - 2022.6", "type": "text"}], "index": 31}, {"bbox": [35, 656, 476, 675], "spans": [{"bbox": [35, 657, 143, 672], "score": 1.0, "content": "徐州工业职业技术学院", "type": "text"}, {"bbox": [211, 656, 238, 675], "score": 1.0, "content": "大专", "type": "text"}, {"bbox": [390, 657, 476, 672], "score": 1.0, "content": "2016.9 - 2019.6", "type": "text"}], "index": 32}], "index": 31.5}], "page_idx": 1, "page_size": [595, 841], "discarded_blocks": [], "para_blocks": [{"type": "text", "bbox": [69, 43, 348, 58], "lines": [], "index": 0, "bbox_fs": [70, 44, 348, 59], "lines_deleted": true}, {"type": "text", "bbox": [54, 61, 560, 113], "lines": [{"bbox": [56, 63, 451, 75], "spans": [{"bbox": [56, 63, 451, 75], "score": 1.0, "content": "2. 负责 Qt 示例鸿蒙化改造，使用 Cmake 结合鸿蒙工程移植 Qt 示例到鸿蒙生态；", "type": "text"}], "index": 1}, {"bbox": [56, 81, 559, 93], "spans": [{"bbox": [56, 81, 559, 93], "score": 1.0, "content": "3. 负责鸿蒙 QT SDK 适配单元测试执行，编写 Python 脚本提取 xml 单元测试结果，功能测试执行，回", "type": "text"}], "index": 2}, {"bbox": [71, 99, 261, 111], "spans": [{"bbox": [71, 99, 261, 111], "score": 1.0, "content": "归测 试执行，编写每个版本的测试报告；", "type": "text"}], "index": 3}], "index": 2, "bbox_fs": [56, 63, 559, 111]}, {"type": "text", "bbox": [35, 122, 179, 136], "lines": [{"bbox": [35, 123, 177, 136], "spans": [{"bbox": [35, 123, 177, 136], "score": 1.0, "content": "项目二: 新一代用电采集系统", "type": "text"}], "index": 4}], "index": 4, "bbox_fs": [35, 123, 177, 136]}, {"type": "text", "bbox": [34, 139, 560, 245], "lines": [{"bbox": [36, 141, 558, 154], "spans": [{"bbox": [36, 141, 558, 154], "score": 1.0, "content": "项目描述: 新一代用电信息采集系统（简称采集 2.0）以国网公司战略为统领，遵循“抽象分层、分类分级、智", "type": "text"}], "index": 5}, {"bbox": [36, 159, 558, 170], "spans": [{"bbox": [36, 159, 558, 170], "score": 1.0, "content": "能支撑、融合应用”的原则，按照“数字驱动、价值 闭环”的设计理念，以核心业务需求为前提，将业务需求进", "type": "text"}], "index": 6}, {"bbox": [36, 177, 558, 190], "spans": [{"bbox": [36, 177, 558, 190], "score": 1.0, "content": "行分解、抽象、归集，形成具有“沉淀、赋能、共享”特性的业务架构，通 过“面向对象”设计方法，设计成性", "type": "text"}], "index": 7}, {"bbox": [36, 195, 559, 208], "spans": [{"bbox": [36, 195, 559, 208], "score": 1.0, "content": "能卓越、功能丰富、安全稳定的客户侧能源互联网基础系统，支持各类用能设备的全接入、全采集、全控制，支持", "type": "text"}], "index": 8}, {"bbox": [43, 213, 556, 226], "spans": [{"bbox": [43, 213, 556, 226], "score": 1.0, "content": "“碳达峰、碳中和”行动方案落地、电力市场化改革、清洁能源消纳和用户能效诊断，服务公司各部门、各专业、", "type": "text"}], "index": 9}, {"bbox": [36, 231, 168, 244], "spans": [{"bbox": [36, 231, 168, 244], "score": 1.0, "content": "各类员工的数据和业务需求。", "type": "text"}], "index": 10}], "index": 7.5, "bbox_fs": [36, 141, 559, 244]}, {"type": "title", "bbox": [35, 249, 82, 262], "lines": [{"bbox": [34, 248, 84, 264], "spans": [{"bbox": [34, 248, 84, 264], "score": 1.0, "content": "主要职责:", "type": "text"}], "index": 11}], "index": 11}, {"type": "index", "bbox": [55, 266, 506, 389], "lines": [{"bbox": [56, 267, 449, 280], "spans": [{"bbox": [56, 267, 449, 280], "score": 1.0, "content": "1. 测试需求分析，测试用例设计与评审组织，编写测试报告，完成系统功能全量测试", "type": "text"}], "index": 12, "is_list_start_line": true}, {"bbox": [55, 284, 501, 298], "spans": [{"bbox": [55, 284, 501, 298], "score": 1.0, "content": "2. 测试环境基于业务逻辑与存储过程完成大批量数据准备，配置定时任务，验证数据链路有效性", "type": "text"}], "index": 13, "is_list_start_line": true}, {"bbox": [56, 303, 175, 316], "spans": [{"bbox": [56, 303, 175, 316], "score": 1.0, "content": "3. 测试环境版本更新维护", "type": "text"}], "index": 14, "is_list_start_line": true}, {"bbox": [56, 321, 317, 335], "spans": [{"bbox": [56, 321, 317, 335], "score": 1.0, "content": "4. 结合日志、F12 定位 Bug，提出 Bug 有效性解决方案", "type": "text"}], "index": 15, "is_list_start_line": true}, {"bbox": [56, 339, 471, 351], "spans": [{"bbox": [56, 339, 471, 351], "score": 1.0, "content": "5. 在内网环境使用 MobaXtarm完成多服务组件之间的链路测试，保证报文传输链路正常", "type": "text"}], "index": 16, "is_list_start_line": true}, {"bbox": [56, 357, 257, 370], "spans": [{"bbox": [56, 357, 257, 370], "score": 1.0, "content": "6. 完成电力 376.1、698 通讯协议专项测试", "type": "text"}], "index": 17, "is_list_start_line": true}, {"bbox": [56, 376, 301, 388], "spans": [{"bbox": [56, 376, 301, 388], "score": 1.0, "content": "7. 编制用户手册，用户培训，协助用户完成系统验收", "type": "text"}], "index": 18, "is_list_start_line": true}], "index": 15, "bbox_fs": [55, 267, 501, 388]}, {"type": "text", "bbox": [35, 398, 159, 412], "lines": [{"bbox": [35, 398, 158, 412], "spans": [{"bbox": [35, 398, 158, 412], "score": 1.0, "content": "项目三: A 客户 BMS 测试", "type": "text"}], "index": 19}], "index": 19, "bbox_fs": [35, 398, 158, 412]}, {"type": "text", "bbox": [35, 416, 560, 467], "lines": [{"bbox": [35, 417, 555, 429], "spans": [{"bbox": [35, 417, 555, 429], "score": 1.0, "content": "项目描述: BMS是 A 客户为提升电池寿命研发的新一代产品。该项目承接的是封装完成后的最后一道测试工序，", "type": "text"}], "index": 20}, {"bbox": [36, 434, 559, 447], "spans": [{"bbox": [36, 434, 559, 447], "score": 1.0, "content": "公司根据客户提出的指标 研发了专为 BMS 芯片而生的 Tester，结合自主研发的自动化生产线，保证了测试准确", "type": "text"}], "index": 21}, {"bbox": [36, 453, 304, 465], "spans": [{"bbox": [36, 453, 304, 465], "score": 1.0, "content": "率的同时使生产效率达到指标之上，使得 改项目延续至今", "type": "text"}], "index": 22}], "index": 21, "bbox_fs": [35, 417, 559, 465]}, {"type": "title", "bbox": [35, 470, 83, 484], "lines": [{"bbox": [34, 470, 86, 485], "spans": [{"bbox": [34, 470, 86, 485], "score": 1.0, "content": "主要职责：", "type": "text"}], "index": 23}], "index": 23}, {"type": "index", "bbox": [54, 490, 394, 596], "lines": [{"bbox": [56, 492, 314, 505], "spans": [{"bbox": [56, 492, 314, 505], "score": 1.0, "content": "1. 出厂测试，测试机出厂时程序的烧录，调整设备参数", "type": "text"}], "index": 24, "is_list_start_line": true}, {"bbox": [55, 509, 368, 524], "spans": [{"bbox": [55, 509, 368, 524], "score": 1.0, "content": "2. 设备立项，协调现场人员设备安装，与客户沟通，反馈客户需求", "type": "text"}], "index": 25, "is_list_start_line": true}, {"bbox": [56, 528, 329, 541], "spans": [{"bbox": [56, 528, 329, 541], "score": 1.0, "content": "3. 负责现场设备运行调优，PLC 程序优化、HMI 功能优化", "type": "text"}], "index": 26, "is_list_start_line": true}, {"bbox": [55, 545, 389, 560], "spans": [{"bbox": [55, 545, 389, 560], "score": 1.0, "content": "4. 负责现场电气接线指导，结构调试，机械手打点，视觉对位模块调试", "type": "text"}], "index": 27, "is_list_start_line": true}, {"bbox": [55, 563, 393, 577], "spans": [{"bbox": [55, 563, 393, 577], "score": 1.0, "content": "5. 现场通信调试，BMS 监控系统，上位机软件、数据库等多个软件安装", "type": "text"}], "index": 28, "is_list_start_line": true}, {"bbox": [56, 582, 356, 595], "spans": [{"bbox": [56, 582, 356, 595], "score": 1.0, "content": "6. 现场测试机异常项统计，及时矫正参数，保证测试机正常运行", "type": "text"}], "index": 29, "is_list_start_line": true}], "index": 26.5, "bbox_fs": [55, 492, 393, 595]}, {"type": "title", "bbox": [48, 612, 107, 630], "lines": [{"bbox": [48, 612, 109, 630], "spans": [{"bbox": [48, 612, 109, 630], "score": 1.0, "content": "教育背景", "type": "text"}], "index": 30}], "index": 30}, {"type": "index", "bbox": [35, 639, 477, 673], "lines": [{"bbox": [35, 638, 475, 656], "spans": [{"bbox": [35, 640, 101, 654], "score": 1.0, "content": "中国矿业大学", "type": "text"}, {"bbox": [211, 638, 239, 656], "score": 1.0, "content": "本科", "type": "text"}, {"bbox": [390, 640, 475, 653], "score": 1.0, "content": "2020.2 - 2022.6", "type": "text"}], "index": 31, "is_list_start_line": true}, {"bbox": [35, 656, 476, 675], "spans": [{"bbox": [35, 657, 143, 672], "score": 1.0, "content": "徐州工业职业技术学院", "type": "text"}, {"bbox": [211, 656, 238, 675], "score": 1.0, "content": "大专", "type": "text"}, {"bbox": [390, 657, 476, 672], "score": 1.0, "content": "2016.9 - 2019.6", "type": "text"}], "index": 32, "is_list_start_line": true}], "index": 31.5, "bbox_fs": [35, 638, 476, 675]}]}], "_backend": "pipeline", "_version_name": "2.1.5"}