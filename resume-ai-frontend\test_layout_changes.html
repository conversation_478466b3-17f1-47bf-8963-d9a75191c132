<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简历布局测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 表格式工作经历样式 */
        .experience-table {
            @apply space-y-6;
        }

        .experience-row {
            @apply border-l-4 border-blue-200 pl-4;
        }

        .experience-grid {
            @apply grid grid-cols-12 gap-2 items-baseline mb-2;
        }

        .company-cell {
            @apply col-span-4 text-lg font-semibold text-gray-900 truncate;
        }

        .position-cell {
            @apply col-span-4 text-gray-700 font-medium truncate;
        }

        .time-cell {
            @apply col-span-4 text-gray-500 text-sm text-right truncate;
        }

        .work-content-list {
            @apply list-disc list-inside space-y-1 text-gray-700;
        }

        /* 表格式教育背景样式 */
        .education-table {
            @apply space-y-4;
        }

        .education-row {
            @apply border-l-4 border-green-200 pl-4;
        }

        .education-grid {
            @apply grid grid-cols-12 gap-2 items-baseline mb-2;
        }

        .school-cell {
            @apply col-span-3 text-lg font-semibold text-gray-900 truncate;
        }

        .degree-cell {
            @apply col-span-2 text-gray-700 font-medium truncate;
        }

        .major-cell {
            @apply col-span-4 text-gray-600 truncate;
        }

        .time-cell {
            @apply col-span-3 text-gray-500 text-sm text-right truncate;
        }

        .courses-info {
            @apply text-gray-700;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .experience-grid,
            .education-grid {
                @apply grid-cols-1 gap-1;
            }

            .company-cell,
            .position-cell,
            .school-cell,
            .degree-cell,
            .major-cell,
            .time-cell {
                @apply col-span-1 text-left;
            }

            .time-cell {
                @apply text-left text-xs;
            }
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto bg-white shadow-lg rounded-lg p-8">
        <h1 class="text-2xl font-bold text-center mb-8">简历布局测试</h1>
        
        <!-- 工作经历测试 -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4 border-b-2 border-blue-500 pb-2">工作经历（表格式对齐）</h2>

            <div class="experience-table">
                <!-- 工作经历项目1 -->
                <div class="experience-row">
                    <!-- 表格式布局 - 公司、职位、时间 -->
                    <div class="experience-grid">
                        <div class="company-cell">腾讯科技有限公司</div>
                        <div class="position-cell">项目经理</div>
                        <div class="time-cell">2017.07-2019.06</div>
                    </div>

                    <!-- 工作内容 -->
                    <div class="work-content-line mt-2">
                        <ul class="work-content-list">
                            <li>负责微信小程序生态产品规划，主导B端工具类小程序开发</li>
                            <li>通过用户调研和数据分析，优化产品体验，NPS提升25分</li>
                            <li>协助制定小程序商业化策略，实现年收入增长50%</li>
                        </ul>
                    </div>
                </div>

                <!-- 工作经历项目2 -->
                <div class="experience-row">
                    <!-- 表格式布局 - 公司、职位、时间 -->
                    <div class="experience-grid">
                        <div class="company-cell">阿里巴巴集团</div>
                        <div class="position-cell">高级项目经理</div>
                        <div class="time-cell">2019.07-2024</div>
                    </div>

                    <!-- 工作内容 -->
                    <div class="work-content-line mt-2">
                        <ul class="work-content-list">
                            <li>负责电商平台用户增长产品线，带领5人产品团队，通过数据驱动优化产品策略</li>
                            <li>协调研发、设计、运营等多部门资源，确保项目按时高质量交付</li>
                            <li>建立了产品标准化流程，提高团队效率20%</li>
                        </ul>
                    </div>
                </div>

                <!-- 工作经历项目3 - 测试对齐效果 -->
                <div class="experience-row">
                    <!-- 表格式布局 - 公司、职位、时间 -->
                    <div class="experience-grid">
                        <div class="company-cell">字节跳动科技有限公司</div>
                        <div class="position-cell">产品总监</div>
                        <div class="time-cell">2024.01-至今</div>
                    </div>

                    <!-- 工作内容 -->
                    <div class="work-content-line mt-2">
                        <ul class="work-content-list">
                            <li>负责抖音商业化产品策略制定和执行</li>
                            <li>管理20人跨职能团队，推动产品创新</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 教育背景测试 -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4 border-b-2 border-green-500 pb-2">教育背景（表格式对齐）</h2>

            <div class="education-table">
                <!-- 教育背景项目1 -->
                <div class="education-row">
                    <!-- 表格式布局 - 学校、学历、专业、时间 -->
                    <div class="education-grid">
                        <div class="school-cell">清华大学</div>
                        <div class="degree-cell">本科</div>
                        <div class="major-cell">计算机科学与技术</div>
                        <div class="time-cell">2010.09-2014.06</div>
                    </div>

                    <!-- 课程信息 -->
                    <div class="education-courses-line mt-2">
                        <div class="courses-info">
                            <strong>主修课程：</strong>数据结构、算法设计、数据库原理、计算机网络
                        </div>
                    </div>

                    <!-- 成绩信息 -->
                    <div class="education-achievements-line mt-2">
                        <strong>成绩：</strong>GPA: 3.8/4.0
                    </div>
                </div>

                <!-- 教育背景项目2 -->
                <div class="education-row">
                    <!-- 表格式布局 - 学校、学历、专业、时间 -->
                    <div class="education-grid">
                        <div class="school-cell">上海交通大学</div>
                        <div class="degree-cell">硕士</div>
                        <div class="major-cell">软件工程</div>
                        <div class="time-cell">2014.09-2017.06</div>
                    </div>

                    <!-- 课程信息 -->
                    <div class="education-courses-line mt-2">
                        <div class="courses-info">
                            <strong>研究方向：</strong>智能与机器学习
                        </div>
                    </div>
                </div>

                <!-- 教育背景项目3 - 测试对齐效果 -->
                <div class="education-row">
                    <!-- 表格式布局 - 学校、学历、专业、时间 -->
                    <div class="education-grid">
                        <div class="school-cell">北京理工大学</div>
                        <div class="degree-cell">博士</div>
                        <div class="major-cell">人工智能与机器学习</div>
                        <div class="time-cell">2017.09-2021.06</div>
                    </div>

                    <!-- 课程信息 -->
                    <div class="education-courses-line mt-2">
                        <div class="courses-info">
                            <strong>研究方向：</strong>深度学习、自然语言处理
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 对比说明 -->
        <div class="bg-blue-50 p-4 rounded-lg">
            <h3 class="font-semibold mb-2">表格式布局说明：</h3>
            <ul class="space-y-1 text-sm text-gray-700">
                <li><strong>工作经历：</strong>使用CSS Grid实现表格式对齐，公司名称、职位、时间垂直对齐</li>
                <li><strong>教育背景：</strong>学校、学历、专业、时间各列垂直对齐，便于比较</li>
                <li><strong>响应式设计：</strong>在小屏幕上自动切换为单列布局</li>
                <li><strong>文本截断：</strong>使用truncate防止长文本破坏布局</li>
                <li><strong>列宽分配：</strong>
                    <ul class="ml-4 mt-1 space-y-1">
                        <li>工作经历：公司(4/12) | 职位(4/12) | 时间(4/12)</li>
                        <li>教育背景：学校(3/12) | 学历(2/12) | 专业(4/12) | 时间(3/12)</li>
                    </ul>
                </li>
                <li>保持左侧彩色边框区分不同模块</li>
            </ul>
        </div>

        <!-- 响应式测试说明 -->
        <div class="bg-yellow-50 p-4 rounded-lg mt-4">
            <h3 class="font-semibold mb-2">响应式测试：</h3>
            <p class="text-sm text-gray-700">
                请调整浏览器窗口大小来测试响应式效果：
            </p>
            <ul class="space-y-1 text-sm text-gray-700 mt-2">
                <li><strong>桌面端 (>1024px)：</strong>完整表格布局，所有列对齐显示</li>
                <li><strong>平板端 (769px-1024px)：</strong>调整列宽比例，保持对齐</li>
                <li><strong>移动端 (<768px)：</strong>切换为单列布局，垂直排列</li>
            </ul>
        </div>
    </div>
</body>
</html>
