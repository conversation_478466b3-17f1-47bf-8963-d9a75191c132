"""
AI服务 - 集成OpenRouter API
"""

import json
import httpx
from typing import Dict, Any, Optional, List
from fastapi import HTT<PERSON>Exception, status
from loguru import logger

from app.core.config import settings
from app.utils.file_handler import FileHandler


class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.base_url = settings.OPENROUTER_BASE_URL
        self.api_key = settings.OPENROUTER_API_KEY
        self.default_model = settings.DEFAULT_MODEL
        
        if not self.api_key:
            logger.warning("OpenRouter API key not configured")
    
    async def _make_request(self, messages: List[Dict[str, str]], model: str = None) -> str:
        """发送请求到OpenRouter API"""
        if not self.api_key:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="AI服务未配置"
            )
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://resumeai.com",
            "X-Title": "Resume.AI"
        }
        
        payload = {
            "model": model or self.default_model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        try:
            async with httpx.AsyncClient(timeout=settings.AI_REQUEST_TIMEOUT) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )
                
                if response.status_code != 200:
                    logger.error(f"OpenRouter API error: {response.status_code} - {response.text}")
                    raise HTTPException(
                        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                        detail="AI服务暂时不可用"
                    )
                
                result = response.json()
                return result["choices"][0]["message"]["content"]
                
        except httpx.TimeoutException:
            raise HTTPException(
                status_code=status.HTTP_504_GATEWAY_TIMEOUT,
                detail="AI服务响应超时"
            )
        except Exception as e:
            logger.error(f"AI service error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="AI服务出现错误"
            )
    
    async def parse_resume(self, resume_text: str) -> Dict[str, Any]:
        """解析简历文本"""

        # 如果没有配置API密钥，使用智能模拟解析
        if not self.api_key:
            logger.info("使用智能模拟解析模式")
            return self._mock_parse_resume(resume_text)

        system_prompt = """# Role: 简历经历抽取助理

## Profile:
- author: iClass
- version: 1.2
- language: 中文
- description: 专业化地将标准简历信息转换为JSON格式，专注于数据的准确处理和分析。

## Background:
在人力资源管理和数据分析领域，将传统的简历中的经历信息转化为结构化的JSON数据格式，对提升处理效率和信息准确性至关重要。这样的转换不仅利于数据分析和存储，还便于信息的共享和交换。

## Goals:
1. 准确无误地解析简历中的标准信息字段。
2. 高效转换信息至JSON格式，确保数据结构正确，信息完整。
3. 在转换过程中保持信息的真实性和准确性。
4. 当前时间是2025年，请用这个时间来计算时间。

## Constraints:
1. 严格识别并精确解析简历中的每个字段。
2. 确保转换过程中不丢失或曲解任何信息。
3. 注意区分work_experience、social_experience、project_experience，training_experience。work_experience主要是指在某个职位上所担任的角色和完成的任务。它通常包含了在哪个公司工作，职务标题，在那个岗位工作了多久，以及主要职责和成就；social_experience、social_experience主要指在非正式工作环境中获取的经验，如志愿者活动、社区服务、学生组织、兴趣小组等；project_experience通常指参与过的具有明确目标、期限和成果的项目。这可以是在学校、工作或者其他环境中完成的项目；training_experience培训经历是指参加过的正式或非正式的教育和训练课程。这可能包括大学课程、专业证书课程、在线学习课程等。
4. 输出的JSON格式必须严格遵守Examples中输出示例结构，便于后续的数据应用。

## Skills:
- 精确的数据解析和格式转换能力。
- 对简历常见字段的深入理解。
- 细致的数据校验和格式化技巧。
- 通过NER识别常见的信息。

## Workflows:
1. 输入: 接收用户提供的标准简历文本。
2. 解析: 细致识别并提取简历中的各个字段。
3. 格式化: 将提取的数据转换为JSON格式，注重数据结构和信息完整性。
4. 校验: 仔细检查JSON结构是否包含education_experience、technical_skills、work_experience、social_experience、project_experience、training_experience、self_evaluation、others等信息。
5. 输出: 向用户返回格式化后的JSON数据。

## Examples:
输出格式示例：
{
    "basic_info": {
        "name": "张三",
        "phone": "13812345678",
        "email": "<EMAIL>",
        "location": "北京市朝阳区", 
        "job_intention": "软件工程师"
    },
    "education_experience": [
        {
            "start_time": "2006-10",
            "end_time": "2009-10",
            "school_name": "湖南师范大学",
            "school_level": "985 211",
            "study_model": "全职",
            "location": "学校所在城市",
            "degree": "本科",
            "major": "新闻学",
            "GPA": "3.81",
            "ranking": "15/320",
            "courses": "新闻与采访，经济学，文学鉴赏，社会舆论心理学",
            "department": "新闻学院"
        }
    ],
    "work_experience": [
        {
            "start_time": "2006-10",
            "end_time": "2009-10",
            "company_name": "湘潭广电中心",
            "department": "摄影部",
            "location": "湘潭",
            "job_title": "摄影师/记者",
            "description": "在湘潭广电中心的时政新闻部实习记者的期间，在实习老师的的指导下初步掌握了相关的新闻记者工作流程，包括采访、摄像、新闻稿的编辑、后期编辑等。并积累了一定的实习经验，完善了所学习的专业知识",
            "industry": "新闻",
            "job_function": "影视/媒体/出版/印刷",
            "company_size": "500-1000人",
            "company_type": "民营",
            "salary": "2000-4000元",
            "underling_num": "10人",
            "report_to": "总经理"
        }
    ],
    "social_experience": [
        {
            "start_time": "2006-10",
            "end_time": "2009-10",
            "organization_name": "大学生市场调研竞赛",
            "department": "科技部",
            "location": "北京",
            "job_title": "参赛者",
            "description": "自己组队进行了比赛，并在此过程之中进行了市场调研、现状研究、模型分析、创新设计、预期成果等。参与的学校社团有学生会、市场部、勤工助学部等并在期间参与多次活动组织与策划任务"
        }
    ],
    "project_experience": [
            {
            "start_time": "2006-10",
            "end_time": "2009-10",
            "project_name": "小看（双平台已上线）",
            "company_name": "小看科技有限公司",
            "location": "北京",
            "job_title": "参加者",
            "job_function": "项目经理",
            "project_background": "（选填）该项目旨在打造中国首款本地化照片识别与分类工具，满足中老年用户对于照片管理和社交分享的多平台需求。",
            "project_responsibilities": "负责设计底层本地相册数据抽取模块，使用 PhotoKit／ALAssetsLibrary 获取图片与视频，按应用来源和美容软件标记分类，且针对屏幕渲染与数据库存储实现速率优化。",
            "project_achievements": "成功上线 iOS 与 Android 双平台，日活达 10 万＋，图片分类准确率提升至 98%，显著减少内存占用与网络请求。",
            "description": "职责：获取底层用户手机本地的相册中的数据集，熟练使用 PhotoKit 和 ALAssetsLibrary 来进行用户本地图片视频集的获取，并且根据每个图片自身的信息不同进行重新划分，例如是否使用过美图秀秀等这一类的图片美化软件修过的图片，以及不同应用接受保存或者创建的图片，同时进行速率优化，在用户的手机"
            }
    ],
    "training_experience": [
        {
            "start_time": "2006-10",
            "end_time": "2009-10",
            "organization_name": "新东方厨师学校",
            "location": "北京",
            "subject": "厨师培训",
            "description": "在一个月的培训中，我熟练掌握了怎么烧一顿好吃的泡面"
        }
    ],
    "self_evaluation": "我是一个好人",
    "technical_skills": [
        "熟悉软件测试流程，具备测试理论与工具知识，并有2年团队管理经验",
        "擅长测试需求分析、测试计划、用例设计、执行及测试报告撰写",
        "精通黑盒测试设计方法：等价类、边界值、错误推断、场景法、因果图判定等",
        "熟练使用缺陷管理工具如禅道、Redmine 进行 Bug 跟踪、回归测试、风险分析",
        "熟悉 Linux 环境与 Shell 脚本编写，能使用 Docker 搭建开发测试环境",
        "熟练使用 Oracle、MySQL SQL，支持存储过程构建测试数据与 SQL 优化",
        "精通 HTTP 协议和接口测试流程，使用 Postman、JMeter 设计并执行接口测试",
        "具有自动化测试设计能力，能搭建 Python 自动化框架实施 Web 和接口自动化测试",
        "可使用 Python + Pandas、NumPy、Faker、xlrd/xlwt、PyHive、cx_Oracle 等开发测试工具",
        "熟悉 Django + Vue3 开发测试平台，理解设备测试与调试原理",
        "掌握PLC、伺服、变频器与机械手系统配置与调试，能改写程序或调整参数响应故障",
        "能够快速理解电路图和原理图，熟练解析自动化硬件连接原理"
    ]
    "others": {
        "language": [
            "英语",
            "汉语"
        ],
        "certificate": [
            "英语四级",
            "会计电算化",
            "普通话证书",
            "证券从业资格证"
        ],
        "awards": [
            "茶学院征文大赛二等奖",
            "三等奖学金",
            "民生银行手机创新大赛三等奖"
        ]
    }
}

重要要求：
1. 只返回纯JSON格式，不要使用```json代码块包裹
2. 不要包含任何解释文字或其他内容
3. 确保JSON格式正确且完整
4. 直接以{开始，以}结束
5. 严格按照示例中的字段结构输出"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请解析以下简历：\n\n{resume_text}"}
        ]

        try:
            response = await self._make_request(messages)
            # 清理响应中的代码块标记
            cleaned_response = self._clean_json_response(response)
            # 尝试解析JSON
            structured_data = json.loads(cleaned_response)
            # 转换为系统期望的格式
            converted_data = self._convert_to_system_format(structured_data)
            return converted_data
        except json.JSONDecodeError as e:
            # 如果JSON解析失败，返回错误
            logger.error(f"Failed to parse AI response as JSON: {response}")
            logger.error(f"JSON decode error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="简历解析失败，请重试"
            )

    def _clean_json_response(self, response: str) -> str:
        """清理AI响应中的代码块标记和多余内容"""
        import re

        # 移除代码块标记
        # 匹配 ```json ... ``` 或 ``` ... ``` 格式
        code_block_pattern = r'```(?:json)?\s*(.*?)\s*```'
        match = re.search(code_block_pattern, response, re.DOTALL)

        if match:
            # 如果找到代码块，提取其中的内容
            cleaned = match.group(1).strip()
            logger.info("✅ 从代码块中提取JSON内容")
        else:
            # 如果没有代码块，直接使用原始响应
            cleaned = response.strip()
            logger.info("✅ 使用原始响应内容")

        # 移除可能的前后缀文本
        # 查找第一个 { 和最后一个 }
        start_idx = cleaned.find('{')
        end_idx = cleaned.rfind('}')

        if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
            cleaned = cleaned[start_idx:end_idx + 1]
            logger.info("✅ 提取JSON对象部分")

        return cleaned

    def _convert_to_system_format(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """将新提示词的输出格式转换为系统期望的格式"""
        if not isinstance(data, dict):
            return data

        # 构建系统期望的格式
        system_format = {
            "基本信息": {},
            "教育经历": [],
            "工作经历": [],
            "项目经历": [],
            "技能": [],
            "证书与奖项": [],
            "语言能力": []
        }

        # 转换教育经历
        if "education_experience" in data:
            education_list = []
            for edu in data["education_experience"]:
                education_item = {
                    "学校": edu.get("school_name", ""),
                    "学历": edu.get("degree", ""),
                    "专业": edu.get("major", ""),
                    "时间": f"{edu.get('start_time', '')}-{edu.get('end_time', '')}",
                    "成绩": edu.get("GPA", "")
                }
                education_list.append(education_item)
            system_format["教育经历"] = education_list

        # 转换工作经历
        if "work_experience" in data:
            work_list = []
            for work in data["work_experience"]:
                # 处理工作内容中的特殊字符，转换为列表格式
                description = work.get("description", "")
                work_content_list = self._parse_work_content(description)

                work_item = {
                    "公司": work.get("company_name", ""),
                    "职位": work.get("job_title", ""),
                    "时间": f"{work.get('start_time', '')}-{work.get('end_time', '')}",
                    "工作内容": work_content_list,
                    "工作成果": ""  # 新格式中没有单独的成果字段
                }
                work_list.append(work_item)
            system_format["工作经历"] = work_list

        # 转换项目经历 - 使用前端期望的字段名
        if "project_experience" in data:
            project_list = []
            for project in data["project_experience"]:
                project_item = {
                    # 前端期望的字段名
                    "name": project.get("project_name", ""),
                    "role": project.get("job_title", ""),
                    "start_date": project.get("start_time", ""),
                    "end_date": project.get("end_time", ""),
                    "background": project.get("project_background", ""),
                    "responsibilities": project.get("project_responsibilities", ""),
                    "achievements": project.get("project_achievements", ""),
                    "description": project.get("description", ""),
                    # 保留中文字段名以兼容其他部分
                    "项目名称": project.get("project_name", ""),
                    "角色": project.get("job_title", ""),
                    "技术栈": [],
                    "时间": f"{project.get('start_time', '')}-{project.get('end_time', '')}",
                    "项目背景": project.get("project_background", ""),
                    "项目职责": project.get("project_responsibilities", ""),
                    "描述": project.get("description", ""),
                    "成果": project.get("project_achievements", "")
                }
                project_list.append(project_item)
            system_format["项目经历"] = project_list

        # 处理其他信息
        if "others" in data:
            others = data["others"]
            if "language" in others:
                system_format["语言能力"] = others["language"]

            # 合并证书和奖项
            certificates = []
            if "certificate" in others:
                for cert in others["certificate"]:
                    certificates.append({
                        "名称": cert,
                        "时间": "",
                        "颁发机构": ""
                    })
            if "awards" in others:
                for award in others["awards"]:
                    certificates.append({
                        "名称": award,
                        "时间": "",
                        "颁发机构": ""
                    })
            system_format["证书与奖项"] = certificates

        # 处理技能 - 从others中提取或从描述中推断
        skills = []
        if "others" in data and "skills" in data["others"]:
            skills = data["others"]["skills"]
        elif "skills" in data:
            # 如果技能是字符串，按逗号分割
            skills_data = data["skills"]
            if isinstance(skills_data, str):
                skills = [skill.strip() for skill in skills_data.split(',') if skill.strip()]
            elif isinstance(skills_data, list):
                skills = skills_data
        # 如果没有单独的技能字段，可以从工作经历描述中提取技能关键词
        # 这里暂时留空，实际使用时可以根据需要添加技能提取逻辑
        system_format["技能"] = skills

        # 构建基本信息
        basic_info = {}
        if "basic_info" in data:
            basic_info_data = data["basic_info"]
            basic_info = {
                "姓名": basic_info_data.get("name", ""),
                "邮箱": basic_info_data.get("email", ""),
                "电话": basic_info_data.get("phone", ""),
                "居住地": basic_info_data.get("location", ""),
                "求职意向": basic_info_data.get("job_intention", "")
            }

        system_format["基本信息"] = basic_info

        return system_format

    def _parse_work_content(self, content: str) -> list:
        """解析工作内容，将包含特殊字符的文本转换为列表格式"""
        if not content:
            return []

        # 按特殊字符分割内容
        import re

        # 分割符：• ● · - 等
        separators = r'[•●·\-]'

        # 先按分号或句号分割，再按特殊字符分割
        parts = re.split(r'[；;。]', content)
        result = []

        for part in parts:
            part = part.strip()
            if not part:
                continue

            # 按特殊字符分割
            sub_parts = re.split(separators, part)
            for sub_part in sub_parts:
                sub_part = sub_part.strip()
                if sub_part and len(sub_part) > 2:  # 过滤掉太短的内容
                    result.append(sub_part)

        # 如果没有分割出内容，返回原始内容
        if not result and content.strip():
            result = [content.strip()]

        return result

# 注意：_standardize_field_names 方法已被 _convert_to_system_format 替代
    # 保留此注释作为参考，实际方法已删除

    def _mock_parse_resume(self, resume_text: str) -> Dict[str, Any]:
        """智能模拟简历解析 - 基于文本内容进行智能提取"""
        import re

        # 初始化结果结构
        result = {
            "基本信息": {
                "姓名": "",
                "邮箱": "",
                "电话": "",
                "居住地": "",
                "求职意向": ""
            },
            "教育经历": [],
            "工作经历": [],
            "项目经历": [],
            "技能": [],
            "证书与奖项": [],
            "语言能力": []
        }

        lines = resume_text.strip().split('\n')

        # 提取基本信息
        for line in lines[:10]:  # 通常基本信息在前几行
            line = line.strip()
            if not line:
                continue

            # 提取姓名（通常是第一行或包含中文姓名的行）
            if not result["基本信息"]["姓名"] and re.match(r'^[\u4e00-\u9fa5]{2,4}$', line):
                result["基本信息"]["姓名"] = line

            # 提取邮箱
            email_match = re.search(r'[\w\.-]+@[\w\.-]+\.\w+', line)
            if email_match:
                result["基本信息"]["邮箱"] = email_match.group()

            # 提取电话
            phone_match = re.search(r'1[3-9]\d{9}', line)
            if phone_match:
                result["基本信息"]["电话"] = phone_match.group()

            # 提取地址
            if any(city in line for city in ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安', '南京', '苏州']):
                if '地址' in line or '居住' in line or '所在地' in line:
                    result["基本信息"]["居住地"] = line.replace('地址：', '').replace('居住地：', '').strip()

        # 提取求职意向
        for line in lines:
            if '求职意向' in line or '应聘职位' in line:
                intention = line.split('：')[-1].strip()
                if intention:
                    result["基本信息"]["求职意向"] = intention
                break

        # 如果没有明确的求职意向，从第二行或职位相关行推断
        if not result["基本信息"]["求职意向"]:
            for line in lines[1:5]:  # 检查前几行
                line = line.strip()
                if any(keyword in line for keyword in ['工程师', '开发', '经理', '设计师', '分析师']):
                    result["基本信息"]["求职意向"] = line
                    break

        # 提取教育经历
        education_section = False
        for line in lines:
            line = line.strip()
            if '教育经历' in line or '教育背景' in line:
                education_section = True
                continue
            elif education_section and ('工作经历' in line or '项目经历' in line):
                education_section = False
                continue

            if education_section and line:
                # 匹配时间格式
                time_match = re.search(r'(\d{4}).*?(\d{4})', line)
                if time_match or any(keyword in line for keyword in ['大学', '学院', '本科', '硕士', '博士']):
                    edu_item = {
                        "学校": "",
                        "学历": "",
                        "专业": "",
                        "时间": "",
                        "成绩": ""
                    }

                    # 提取学校
                    for school_keyword in ['大学', '学院', '学校']:
                        if school_keyword in line:
                            school_match = re.search(r'[\u4e00-\u9fa5]+' + school_keyword, line)
                            if school_match:
                                edu_item["学校"] = school_match.group()
                                break

                    # 提取学历
                    for degree in ['博士', '硕士', '本科', '专科']:
                        if degree in line:
                            edu_item["学历"] = degree
                            break

                    # 提取专业
                    if '专业' in line:
                        major_match = re.search(r'专业[：:]?\s*([^\s]+)', line)
                        if major_match:
                            edu_item["专业"] = major_match.group(1)

                    # 提取时间
                    if time_match:
                        edu_item["时间"] = f"{time_match.group(1)}-{time_match.group(2)}"

                    if edu_item["学校"] or edu_item["专业"]:
                        result["教育经历"].append(edu_item)

        # 提取工作经历
        work_section = False
        current_work = None
        for i, line in enumerate(lines):
            line = line.strip()
            if '工作经历' in line:
                work_section = True
                continue
            elif work_section and ('项目经历' in line or '技能' in line):
                work_section = False
                continue

            if work_section and line:
                # 检查是否是新的工作经历（包含时间和公司）
                time_match = re.search(r'(\d{4}).*?(\d{4})', line)
                if time_match:
                    if current_work:
                        result["工作经历"].append(current_work)

                    current_work = {
                        "公司": "",
                        "职位": "",
                        "时间": f"{time_match.group(1)}-{time_match.group(2)}",
                        "工作内容": "",
                        "工作成果": ""
                    }

                    # 提取公司名
                    company_match = re.search(r'[\u4e00-\u9fa5]+(?:公司|科技|集团|有限|技术)', line)
                    if company_match:
                        current_work["公司"] = company_match.group()

                    # 提取职位 - 检查下一行是否包含职位信息
                    if i + 1 < len(lines):
                        next_line = lines[i + 1].strip()
                        position_keywords = ['工程师', '开发', '经理', '主管', '总监', '专员', '助理', '架构师', '设计师']
                        for keyword in position_keywords:
                            if keyword in next_line:
                                pos_match = re.search(r'[\u4e00-\u9fa5]*' + keyword, next_line)
                                if pos_match:
                                    current_work["职位"] = pos_match.group()
                                    break

                    # 如果当前行就包含职位信息
                    if not current_work["职位"]:
                        position_keywords = ['工程师', '开发', '经理', '主管', '总监', '专员', '助理', '架构师', '设计师']
                        for keyword in position_keywords:
                            if keyword in line:
                                pos_match = re.search(r'[\u4e00-\u9fa5]*' + keyword, line)
                                if pos_match:
                                    current_work["职位"] = pos_match.group()
                                    break

                elif current_work and (line.startswith('-') or line.startswith('•')):
                    # 工作内容
                    content = line[1:].strip()
                    if current_work["工作内容"]:
                        current_work["工作内容"] += "; " + content
                    else:
                        current_work["工作内容"] = content

        if current_work:
            result["工作经历"].append(current_work)

        # 提取项目经历
        project_section = False
        current_project = None
        for i, line in enumerate(lines):
            line = line.strip()
            if '项目经历' in line:
                project_section = True
                continue
            elif project_section and ('技能' in line or '证书' in line):
                project_section = False
                continue

            if project_section and line:
                # 检查是否是新的项目（通常项目名在单独一行或包含时间）
                time_match = re.search(r'\((\d{4}).*?(\d{4})\)', line)
                if time_match or (not line.startswith('-') and not line.startswith('•') and
                                '项目' in line or '系统' in line or '平台' in line):
                    if current_project:
                        result["项目经历"].append(current_project)

                    current_project = {
                        "项目名称": "",
                        "角色": "",
                        "技术栈": [],
                        "时间": "",
                        "描述": "",
                        "成果": ""
                    }

                    # 提取项目名称
                    if time_match:
                        current_project["项目名称"] = line.replace(time_match.group(), '').strip()
                        current_project["时间"] = f"{time_match.group(1)}-{time_match.group(2)}"
                    else:
                        current_project["项目名称"] = line

                    # 检查后续行的项目信息
                    for j in range(i + 1, min(i + 6, len(lines))):
                        next_line = lines[j].strip()
                        if not next_line or next_line.startswith('项目'):
                            break

                        if '角色' in next_line or '职责' in next_line:
                            current_project["角色"] = next_line.split('：')[-1].strip()
                        elif '技术栈' in next_line or '技术' in next_line:
                            tech_text = next_line.split('：')[-1].strip()
                            current_project["技术栈"] = [tech.strip() for tech in re.split(r'[,，、]', tech_text) if tech.strip()]
                        elif '描述' in next_line:
                            current_project["描述"] = next_line.split('：')[-1].strip()

                elif current_project and (line.startswith('-') or line.startswith('•')):
                    # 项目描述或成果
                    content = line[1:].strip()
                    if '成果' in content or '效果' in content or '提升' in content:
                        if current_project["成果"]:
                            current_project["成果"] += "; " + content
                        else:
                            current_project["成果"] = content
                    else:
                        if current_project["描述"]:
                            current_project["描述"] += "; " + content
                        else:
                            current_project["描述"] = content

        if current_project:
            result["项目经历"].append(current_project)

        # 提取技能
        skill_section = False
        for line in lines:
            line = line.strip()
            if '技能' in line and ('清单' in line or '能力' in line or line == '技能清单:' or line == '技能:'):
                skill_section = True
                continue
            elif skill_section and ('证书' in line or '奖项' in line or '语言' in line):
                skill_section = False
                continue

            if skill_section and line:
                # 提取技能列表
                if '：' in line or ':' in line:
                    skills_text = line.split('：')[-1] if '：' in line else line.split(':')[-1]
                    skills = [skill.strip() for skill in re.split(r'[,，、\s]', skills_text) if skill.strip()]
                    result["技能"].extend(skills)
                else:
                    # 直接按分隔符分割
                    skills = [skill.strip() for skill in re.split(r'[,，、\s]', line) if skill.strip()]
                    result["技能"].extend(skills)

        # 提取证书与奖项
        cert_section = False
        for line in lines:
            line = line.strip()
            if '证书' in line or '奖项' in line:
                cert_section = True
                continue
            elif cert_section and '语言' in line:
                cert_section = False
                continue

            if cert_section and line:
                cert_item = {
                    "名称": line,
                    "时间": "",
                    "颁发机构": ""
                }

                # 提取时间
                time_match = re.search(r'\d{4}年?', line)
                if time_match:
                    cert_item["时间"] = time_match.group()
                    cert_item["名称"] = line.replace(time_match.group(), '').strip()

                result["证书与奖项"].append(cert_item)

        # 提取语言能力
        lang_section = False
        for line in lines:
            line = line.strip()
            if '语言能力' in line or '语言水平' in line:
                lang_section = True
                continue

            if lang_section and line:
                if '：' in line:
                    lang_text = line.split('：')[-1]
                    result["语言能力"].append(lang_text)
                else:
                    result["语言能力"].append(line)

        return result
    
    async def parse_job_description(self, jd_text: str) -> Dict[str, Any]:
        """解析岗位JD"""
        system_prompt = """你是一个专业的岗位JD解析助手。请将以下岗位描述解析为结构化的JSON格式。

请按照以下格式返回JSON数据：
{
    "岗位名称": "",
    "部门": "",
    "职责关键词": [],
    "要求技能": [],
    "经验要求": "",
    "学历要求": "",
    "加分项": [],
    "所在城市": "",
    "公司产品关键词": []
}

请确保返回的是有效的JSON格式，不要包含任何其他文本。"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请解析以下岗位JD：\n\n{jd_text}"}
        ]
        
        try:
            response = await self._make_request(messages)
            structured_data = json.loads(response)
            return structured_data
        except json.JSONDecodeError:
            logger.error(f"Failed to parse AI response as JSON: {response}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="JD解析失败，请重试"
            )
    
    async def analyze_match(self, resume_data: Dict[str, Any], jd_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析简历与JD的匹配度"""
        system_prompt = """你是一个专业的简历匹配分析师。请分析简历与岗位JD的匹配度，并给出详细的评分和建议。

评分标准：
- 技能匹配度：0-30分
- 项目相关性：0-30分  
- 表达精准性：0-20分
- 关键词覆盖度：0-20分
- 总分：0-100分

请按照以下格式返回JSON数据：
{
    "匹配评分": {
        "技能匹配度": 0,
        "项目相关性": 0,
        "表达精准性": 0,
        "关键词覆盖度": 0,
        "总分": 0
    },
    "已匹配技能": [],
    "缺失技能": [],
    "已匹配关键词": [],
    "缺失关键词": [],
    "优势亮点": [],
    "需要改进": [],
    "优化建议": [
        {
            "模块": "",
            "建议": "",
            "优先级": 1
        }
    ]
}

请确保返回的是有效的JSON格式，不要包含任何其他文本。"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"简历数据：\n{json.dumps(resume_data, ensure_ascii=False, indent=2)}\n\n岗位JD数据：\n{json.dumps(jd_data, ensure_ascii=False, indent=2)}"}
        ]
        
        try:
            response = await self._make_request(messages)
            analysis_result = json.loads(response)
            return analysis_result
        except json.JSONDecodeError:
            logger.error(f"Failed to parse AI response as JSON: {response}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="匹配分析失败，请重试"
            )
    
    async def generate_optimization_suggestions(
        self, 
        resume_data: Dict[str, Any], 
        jd_data: Dict[str, Any], 
        match_result: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成优化建议"""
        system_prompt = """你是一个专业的简历优化顾问。基于简历数据、岗位JD和匹配分析结果，请生成具体的优化建议。

请按照以下格式返回JSON数组：
[
    {
        "模块": "项目经历",
        "建议类型": "内容优化",
        "原始内容": "",
        "建议内容": "",
        "原因": "",
        "优先级": 1
    }
]

优先级：1-5，1为最高优先级
模块可以是：基本信息、教育经历、工作经历、项目经历、技能、证书与奖项等

请确保返回的是有效的JSON格式，不要包含任何其他文本。"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"简历数据：\n{json.dumps(resume_data, ensure_ascii=False, indent=2)}\n\n岗位JD数据：\n{json.dumps(jd_data, ensure_ascii=False, indent=2)}\n\n匹配分析结果：\n{json.dumps(match_result, ensure_ascii=False, indent=2)}"}
        ]
        
        try:
            response = await self._make_request(messages)
            suggestions = json.loads(response)
            return suggestions
        except json.JSONDecodeError:
            logger.error(f"Failed to parse AI response as JSON: {response}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="优化建议生成失败，请重试"
            )

    async def parse_resume_file(self, file_path: str, file_type: str) -> Dict[str, Any]:
        """解析简历文件 - 先用Python库提取文本，再用AI处理"""
        logger.info(f"开始解析简历文件: {file_path}, 类型: {file_type}")

        try:
            # 第一步：使用Python库提取文本内容
            logger.info("第一步：使用Python库提取文本内容")
            file_handler = FileHandler()
            extracted_text = file_handler.extract_text_from_file(file_path, file_type)

            if not extracted_text or len(extracted_text.strip()) < 10:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="文件内容为空或过短，请检查文件是否正确"
                )

            logger.info(f"文本提取成功，长度: {len(extracted_text)} 字符")
            logger.debug(f"提取的文本内容（前200字符）: {extracted_text[:200]}...")

            # 第二步：使用AI服务解析结构化数据
            logger.info("第二步：使用AI服务解析结构化数据")
            structured_data = await self.parse_resume(extracted_text)

            # 返回完整结果
            result = {
                "original_text": extracted_text,
                "structured_data": structured_data,
                "file_info": {
                    "file_path": file_path,
                    "file_type": file_type,
                    "text_length": len(extracted_text)
                }
            }

            logger.info("简历文件解析完成")
            return result

        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"简历文件解析失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"简历文件解析失败: {str(e)}"
            )
