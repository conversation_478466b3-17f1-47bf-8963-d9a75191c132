<template>
  <div class="resume-preview">
    <div v-if="resume" class="resume-template">
      <!-- 简历头部 -->
      <div class="resume-header">
        <div class="candidate-info">
          <h1 class="candidate-name">{{ getCandidateName() }}</h1>
          <div class="contact-info">
            <div v-if="getCandidatePhone()" class="contact-item">
              <span class="contact-label">电话：</span>
              <span>{{ getCandidatePhone() }}</span>
            </div>
            <div v-if="getCandidateEmail()" class="contact-item">
              <span class="contact-label">邮箱：</span>
              <span>{{ getCandidateEmail() }}</span>
            </div>
            <div v-if="getCandidateLocation()" class="contact-item">
              <span class="contact-label">地址：</span>
              <span>{{ getCandidateLocation() }}</span>
            </div>
          </div>
        </div>
        <div class="avatar-placeholder">
          <div class="avatar-icon">👤</div>
        </div>
      </div>

      <!-- 个人简介 -->
      <div v-if="getPersonalSummary()" class="resume-section">
        <h2 class="section-title">个人简介</h2>
        <div class="section-content">
          <p class="summary-text">{{ getPersonalSummary() }}</p>
        </div>
      </div>

      <!-- 工作经历 -->
      <div class="resume-section">
        <h2 class="section-title">工作经历</h2>
        <div class="section-content">
          <div v-if="getWorkExperience().length > 0" class="experience-list">
            <!-- 表格式布局的工作经历 -->
            <div class="experience-table">
              <div v-for="(exp, index) in getWorkExperience()" :key="index" class="experience-row">
                <!-- 第一行：表格式布局 - 公司、职位、时间 -->
                <div class="experience-grid">
                  <div class="company-cell">{{ exp.公司 || '公司名称' }}</div>
                  <div class="position-cell">{{ exp.职位 || '职位名称' }}</div>
                  <div class="time-cell">{{ exp.时间 || formatWorkTime(exp.开始时间, exp.结束时间) }}</div>
                </div>

                <!-- 第二行：工作内容 -->
                <div v-if="exp.工作内容 && exp.工作内容.length > 0" class="work-content-line">
                  <ul class="work-content-list">
                    <li v-for="(item, i) in exp.工作内容" :key="i">
                      {{ item }}
                    </li>
                  </ul>
                </div>

                <!-- 工作成果（如果有） -->
                <div v-if="exp.工作成果" class="work-achievements-line">
                  <strong>工作成果：</strong>{{ exp.工作成果 }}
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-content">
            <p>暂无工作经历信息</p>
          </div>
        </div>
      </div>

      <!-- 教育背景 -->
      <div class="resume-section">
        <h2 class="section-title">教育背景</h2>
        <div class="section-content">
          <div v-if="getEducation().length > 0" class="education-list">
            <!-- 表格式布局的教育背景 -->
            <div class="education-table">
              <div v-for="(edu, index) in getEducation()" :key="index" class="education-row">
                <!-- 第一行：表格式布局 - 学校、学历、专业、时间 -->
                <div class="education-grid">
                  <div class="school-cell">{{ edu.学校 || '学校名称' }}</div>
                  <div class="degree-cell">{{ edu.学历 || '学历' }}</div>
                  <div class="major-cell">{{ edu.专业 || '' }}</div>
                  <div class="time-cell">{{ edu.时间 || formatEducationTime(edu.开始时间, edu.结束时间) }}</div>
                </div>

                <!-- 第二行：课程信息 -->
                <div v-if="edu.课程 || edu.详细信息" class="education-courses-line">
                  <div v-if="edu.课程" class="courses-info">
                    <strong>主修课程：</strong>{{ edu.课程 }}
                  </div>
                  <div v-else-if="edu.详细信息 && edu.详细信息.length > 0" class="courses-info">
                    <ul class="courses-list">
                      <li v-for="(detail, i) in edu.详细信息" :key="i">
                        {{ detail }}
                      </li>
                    </ul>
                  </div>
                </div>

                <!-- 成绩信息（如果有） -->
                <div v-if="edu.成绩" class="education-achievements-line">
                  <strong>成绩：</strong>{{ edu.成绩 }}
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-content">
            <p>暂无教育背景信息</p>
          </div>
        </div>
      </div>

      <!-- 项目经验 -->
      <div class="resume-section">
        <h2 class="section-title">项目经验</h2>
        <div class="section-content">
          <div v-if="getProjectExperience().length > 0" class="project-list">
            <div v-for="(project, index) in getProjectExperience()" :key="index" class="project-item">
              <div class="project-header">
                <div class="project-main">
                  <h3 class="project-title">{{ project.项目名称 || '项目名称' }}</h3>
                  <div class="project-role">{{ project.角色 || '项目角色' }}</div>
                  <div v-if="project.company" class="project-company">{{ project.company }}</div>
                </div>
                <div class="project-date">
                  {{ project.时间 || '' }}
                </div>
              </div>
              <div v-if="project.项目背景 || project.项目职责 || project.成果" class="project-description">
                <div v-if="project.项目背景" class="project-background">
                  <h4>项目背景：</h4>
                  <p>{{ project.项目背景 }}</p>
                </div>
                <div v-if="project.项目职责" class="project-responsibilities">
                  <h4>项目职责：</h4>
                  <ul class="description-list">
                    <li v-for="(desc, i) in project.项目职责" :key="i">
                      {{ desc }}
                    </li>
                  </ul>
                </div>
                <div v-if="project.成果" class="project-achievements">
                  <h4>项目成果：</h4>
                  <ul class="description-list">
                    <li v-for="(achievement, i) in project.成果" :key="i">
                      {{ achievement }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-content">
            <p>暂无项目经验信息</p>
          </div>
        </div>
      </div>

      <!-- 专业技能 -->
      <div class="resume-section">
        <h2 class="section-title">专业技能</h2>
        <div class="section-content">
          <div v-if="getSkills().length > 0" class="skills-grid">
            <div v-for="skill in getSkills()" :key="skill" class="skill-item">
              {{ skill }}
            </div>
          </div>
          <div v-else class="empty-content">
            <p>暂无技能信息</p>
          </div>
        </div>
      </div>


    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">📄</div>
      <p class="empty-text">请选择一份简历查看预览</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResumePreview',
  props: {
    resume: {
      type: Object,
      default : null
    }
  },
  watch: {
    resume: {
      handler(newResume) {
        console.log('🔍 ResumePreview 接收到新的简历数据:', newResume)
      },
      immediate: true
    }
  },
  methods: {
    getCandidateName() {
      // 直接从structured_data获取
      return this.resume?.structured_data?.基本信息?.姓名 || '姓名'
    },

    getCandidatePhone() {
      return this.resume?.structured_data?.基本信息?.电话 || ''
    },

    getCandidateEmail() {
      return this.resume?.structured_data?.基本信息?.邮箱 || ''
    },

    getCandidateLocation() {
      return this.resume?.candidate_location ||
             this.resume?.structured_data?.基本信息?.居住地 ||
             this.resume?.location ||
             ''
    },

    getPersonalSummary() {
      return this.resume?.structured_data?.基本信息?.求职意向 || ''
    },

    getWorkExperience() {
      // 直接使用后端返回的结构化数据，并处理工作内容格式
      const workExp = this.resume?.structured_data?.工作经历 || []
      return workExp.map(exp => ({
        ...exp,
        工作内容: this.parseWorkContent(exp.工作内容)
      }))
    },

    getEducation() {
      // 直接使用后端返回的结构化数据
      return this.resume?.structured_data?.教育经历 || []
    },

    getSkills() {
      if (!this.resume) return []

      const skills = new Set()

      // 从 structured_data 的中文字段获取（主要数据源）
      if (this.resume.structured_data?.技能 && Array.isArray(this.resume.structured_data.技能)) {
        this.resume.structured_data.技能.forEach(skill => {
          if (skill && skill.trim()) skills.add(skill.trim())
        })
      }

      // 从 skills 字段获取
      if (this.resume.skills) {
        if (Array.isArray(this.resume.skills)) {
          this.resume.skills.forEach(skill => skills.add(skill))
        } else if (typeof this.resume.skills === 'string') {
          this.resume.skills.split(/[,，、\s]+/).forEach(skill => {
            if (skill.trim()) skills.add(skill.trim())
          })
        }
      }

      // 从 extracted_skills 字段获取
      if (this.resume.extracted_skills && Array.isArray(this.resume.extracted_skills)) {
        this.resume.extracted_skills.forEach(skill => skills.add(skill))
      }

      return Array.from(skills).filter(skill => skill && skill.length > 0)
    },

    getProjectExperience() {
      // 直接使用后端返回的结构化数据，并处理项目职责和成果格式
      const projectExp = this.resume?.structured_data?.项目经历 || []
      return projectExp.map(project => ({
        ...project,
        项目职责: this.parseWorkContent(project.项目职责),
        成果: this.parseWorkContent(project.成果)
      }))
    },

    formatWorkTime(startTime, endTime) {
      if (!startTime && !endTime) return ''
      if (!startTime) return endTime || ''
      if (!endTime) return startTime || ''
      return `${startTime} - ${endTime}`
    },

    formatEducationTime(startTime, endTime) {
      if (!startTime && !endTime) return ''
      if (!startTime) return endTime || ''
      if (!endTime) return startTime || ''
      return `${startTime} - ${endTime}`
    },

    formatExperienceDate(startDate, endDate) {
      const start = startDate ? this.formatDateShort(startDate) : ''
      const end = endDate ? this.formatDateShort(endDate) : '至今'
      return start && end ? `${start} - ${end}` : ''
    },

    formatEducationDate(startDate, endDate) {
      const start = startDate ? this.formatDateShort(startDate) : ''
      const end = endDate ? this.formatDateShort(endDate) : ''
      return start && end ? `${start} - ${end}` : ''
    },

    formatProjectDate(startDate, endDate) {
      const start = startDate ? this.formatDateShort(startDate) : ''
      const end = endDate ? this.formatDateShort(endDate) : ''
      return start && end ? `${start} - ${end}` : ''
    },

    formatDateShort(dateString) {
      if (!dateString) return ''
      try {
        const date = new Date(dateString)
        return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}`
      } catch (error) {
        return dateString
      }
    },

    parseWorkContent(content) {
      // 如果已经是数组，直接返回
      if (Array.isArray(content)) {
        return content
      }

      // 如果是字符串，按特殊字符分割
      if (typeof content === 'string' && content.trim()) {
        // 分割符：• ● · - 等
        const separators = /[•●·\-；;。]/

        // 先按分号或句号分割，再按特殊字符分割
        const parts = content.split(/[；;。]/)
        const result = []

        for (const part of parts) {
          const trimmedPart = part.trim()
          if (!trimmedPart) continue

          // 按特殊字符分割
          const subParts = trimmedPart.split(separators)
          for (const subPart of subParts) {
            const trimmedSubPart = subPart.trim()
            if (trimmedSubPart && trimmedSubPart.length > 2) {
              result.push(trimmedSubPart)
            }
          }
        }

        // 如果没有分割出内容，返回原始内容
        return result.length > 0 ? result : [content.trim()]
      }

      return []
    },

    parseDescription(description) {
      if (!description) return []
      if (Array.isArray(description)) return description

      // 清理特殊符号
      let cleanText = description
        // 处理 ${数字+} 格式
        .replace(/\$\{\s*(\d+)\s*\+\s*\}/g, '$1+')  // ${20+} -> 20+
        .replace(/\$\{\s*(\d+)\s*(\d+)\s*\+\s*\}/g, '$1$2+')  // ${2 0+} -> 20+

        // 处理 ${数字} 格式
        .replace(/\$\{\s*(\d+)\s*(\d+)\s*\}/g, '$1$2')  // ${1 5} -> 15
        .replace(/\$\{\s*(\d+)\s*\}/g, '$1')  // ${15} -> 15

        // 处理 $+$ 格式
        .replace(/\$\+\$/g, '+')  // $+$ -> +

        // 处理百分比格式
        .replace(/\$\s*(\d+)\s*\\\s*%\s*\$/g, '$1%')  // $4 5 \%$ -> 45%
        .replace(/\$\s*(\d+)\s*(\d+)\s*\\\s*%\s*\$/g, '$1$2%')  // $2 8 \%$ -> 28%
        .replace(/\$\s*(\d+)\s*(\d+)\s*\s*\\\s*%\s*\$/g, '$1$2%')  // $3 0 \%$ -> 30%

      // 在列表标识符前添加换行，然后按行分割
      const listItems = cleanText
        .replace(/([^•●·\n])([•●·])/g, '$1\n$2')  // 在列表符号前添加换行
        .split(/\n/)  // 按换行符分割
        .filter(line => line.trim())  // 过滤空行
        .map(line => {
          // 清理每行的内容
          return line.trim()
            .replace(/^[•●·]\s*/, '')  // 移除开头的列表符号
            .replace(/\s+/g, ' ')  // 合并多个空格
            .trim()
        })
        .filter(line => line.length > 0)  // 过滤空内容

      return listItems
    },

  }
}
</script>

<style scoped>
.resume-preview {
  @apply h-full bg-white;
}

.resume-template {
  @apply p-6 max-w-4xl mx-auto bg-white shadow-sm;
  font-family: 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
}

/* 简历头部 */
.resume-header {
  @apply flex items-start justify-between mb-8 pb-6 border-b-2 border-blue-500;
}

.candidate-info {
  @apply flex-1;
}

.candidate-name {
  @apply text-3xl font-bold text-gray-900 mb-3;
}

.contact-info {
  @apply space-y-1;
}

.contact-item {
  @apply flex items-center text-sm text-gray-600;
}

.contact-label {
  @apply font-medium mr-2;
}

.avatar-placeholder {
  @apply w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center ml-6;
}

.avatar-icon {
  @apply text-3xl text-gray-400;
}

/* 简历章节 */
.resume-section {
  @apply mb-8;
}

.section-title {
  @apply text-xl font-bold text-gray-900 mb-4 pb-2 border-b border-gray-300;
  color: #2563eb;
}

.section-content {
  @apply pl-0;
}

/* 个人简介 */
.summary-text {
  @apply text-gray-700 leading-relaxed;
}

/* 工作经历 */
.experience-list {
  @apply space-y-6;
}

/* 表格式工作经历布局 */
.experience-table {
  @apply space-y-6;
}

.experience-row {
  @apply border-l-4 border-blue-200 pl-4;
}

.experience-grid {
  @apply grid grid-cols-12 gap-2 items-baseline mb-2;
}

.company-cell {
  @apply col-span-4 text-lg font-semibold text-gray-900 truncate;
}

.position-cell {
  @apply col-span-4 text-gray-700 font-medium truncate;
}

.time-cell {
  @apply col-span-4 text-gray-500 text-sm text-right truncate;
}

.work-content-line {
  @apply mt-2;
}

.work-content-list {
  @apply list-disc list-inside space-y-1 text-gray-700;
}

.work-achievements-line {
  @apply mt-2 text-gray-700;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .experience-grid {
    @apply grid-cols-1 gap-1;
  }

  .company-cell,
  .position-cell,
  .time-cell {
    @apply col-span-1 text-left;
  }

  .time-cell {
    @apply text-left text-xs;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .company-cell {
    @apply col-span-5;
  }

  .position-cell {
    @apply col-span-4;
  }

  .time-cell {
    @apply col-span-3;
  }
}

/* 保留旧样式以兼容 */
.experience-header {
  @apply flex items-start justify-between mb-2;
}

.experience-main {
  @apply flex-1;
}

.job-title {
  @apply text-lg font-semibold text-gray-900 mb-1;
}

.company-name {
  @apply text-gray-600 font-medium;
}

.experience-date {
  @apply text-sm text-gray-500 font-medium;
}

.experience-description {
  @apply mt-3;
}

.description-list {
  @apply list-disc list-inside space-y-1 text-gray-700;
}

.description-list li {
  @apply text-sm leading-relaxed;
}

/* 教育背景 */
.education-list {
  @apply space-y-4;
}

/* 表格式教育背景布局 */
.education-table {
  @apply space-y-4;
}

.education-row {
  @apply border-l-4 border-green-200 pl-4;
}

.education-grid {
  @apply grid grid-cols-12 gap-2 items-baseline mb-2;
}

.school-cell {
  @apply col-span-3 text-lg font-semibold text-gray-900 truncate;
}

.degree-cell {
  @apply col-span-2 text-gray-700 font-medium truncate;
}

.major-cell {
  @apply col-span-4 text-gray-600 truncate;
}

.time-cell {
  @apply col-span-3 text-gray-500 text-sm text-right truncate;
}

.education-courses-line {
  @apply mt-2;
}

.courses-info {
  @apply text-gray-700;
}

.courses-list {
  @apply list-disc list-inside space-y-1 text-gray-700 text-sm;
}

.education-achievements-line {
  @apply mt-2 text-gray-700;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .education-grid {
    @apply grid-cols-1 gap-1;
  }

  .school-cell,
  .degree-cell,
  .major-cell,
  .time-cell {
    @apply col-span-1 text-left;
  }

  .time-cell {
    @apply text-left text-xs;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .school-cell {
    @apply col-span-4;
  }

  .degree-cell {
    @apply col-span-2;
  }

  .major-cell {
    @apply col-span-3;
  }

  .time-cell {
    @apply col-span-3;
  }
}

/* 保留旧样式以兼容 */
.education-header {
  @apply flex items-start justify-between;
}

.education-main {
  @apply flex-1;
}

.school-name {
  @apply text-lg font-semibold text-gray-900 mb-1;
}

.degree-info {
  @apply space-x-2;
}

.degree {
  @apply text-gray-600 font-medium;
}

.major {
  @apply text-gray-500;
}

.education-date {
  @apply text-sm text-gray-500 font-medium;
}

/* 项目经验 */
.project-list {
  @apply space-y-8;
}

.project-item {
  @apply border-l-4 border-purple-200 pl-4;
}

.project-header {
  @apply flex items-start justify-between mb-3;
}

.project-main {
  @apply flex-1;
}

.project-title {
  @apply text-lg font-semibold text-gray-900 mb-1;
}

.project-role {
  @apply text-purple-600 font-medium mb-1;
}

.project-company {
  @apply text-gray-600 text-sm;
}

.project-date {
  @apply text-sm text-gray-500 font-medium;
}

.project-description {
  @apply mt-4 space-y-3;
}

.project-background,
.project-responsibilities,
.project-achievements {
  @apply text-sm;
}

.project-background h4,
.project-responsibilities h4,
.project-achievements h4 {
  @apply font-semibold text-gray-800 mb-1;
}

.project-background p {
  @apply text-gray-700 leading-relaxed;
}

/* 专业技能 */
.skills-grid {
  @apply grid grid-cols-2 gap-2;
}

.skill-item {
  @apply px-3 py-2 bg-blue-50 text-blue-800 text-sm rounded-lg font-medium;
}

/* 空状态 */
.empty-content {
  @apply text-center py-8 text-gray-500;
}

.empty-state {
  @apply text-center py-12;
}

.empty-icon {
  @apply text-4xl mb-4;
}

.empty-text {
  @apply text-gray-500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resume-template {
    @apply p-4;
  }

  .resume-header {
    @apply flex-col items-start;
  }

  .avatar-placeholder {
    @apply ml-0 mt-4;
  }

  .experience-header,
  .education-header {
    @apply flex-col items-start;
  }

  .experience-date,
  .education-date {
    @apply mt-1;
  }

  .skills-grid {
    @apply grid-cols-1;
  }
}
</style>
